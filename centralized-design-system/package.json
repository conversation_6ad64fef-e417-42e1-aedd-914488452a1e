{"name": "centralized-design-system", "version": "1.0.0", "description": "Edcast Centralized Design System (CDS)", "main": "index.js", "scripts": {"test": "cypress run --component", "lint": "eslint src --ext js,jsx"}, "repository": {"type": "git", "url": "git+https://bitbucket.csod.com/scm/ent-lx/centralized-design-system.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://bitbucket.csod.com/scm/ent-lx/centralized-design-system/issues"}, "homepage": "https://bitbucket.csod.com/scm/ent-lx/centralized-design-system#readme", "husky": {"hooks": {"pre-commit": "lint-staged --config=.lintstagedrc.json"}}, "overrides": {"react-rte": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "ua-parser-js": "^1.0.35"}, "devDependencies": {"@babel/eslint-parser": "^7.23.3", "@babel/preset-env": "^7.25.3", "@babel/preset-react": "^7.24.7", "@babel/runtime": "^7.23.8", "babel-loader": "^9.1.3", "css-loader": "^7.1.2", "cypress": "^13.13.2", "eslint": "^8.56.0", "eslint-plugin-cypress": "^3.4.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-promise": "^3.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-sonarjs": "^0.21.0", "husky": "^1.3.1", "lint-staged": "^10.5.4", "lodash": "^4.17.21", "prettier": "^1.16.4", "react-loadable": "5.5.0", "react-rte": "^0.16.5", "react-timeago": "7.x.x", "sass": "^1.77.8", "sass-loader": "^16.0.0", "style-loader": "^4.0.0", "ts-loader": "^9.5.1", "typescript": "^5.4.2", "webpack": "^5.93.0", "webpack-cli": "^5.1.4"}, "dependencies": {"@babel/core": "^7.23.7", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/material": "^5.15.4", "@rails/activestorage": "^8.0.100", "@uppy/audio": "2.0.1", "@uppy/aws-s3": "4.1.0", "@uppy/core": "4.2.3", "@uppy/dashboard": "4.1.1", "@uppy/drop-target": "3.0.1", "@uppy/image-editor": "3.1.0", "@uppy/webcam": "4.0.2", "bootstrap-daterangepicker": "^3.1.0", "ckeditor4-react": "^4.3.0", "edc-web-sdk": "git+ssh://********************************:7999/ent-lx/edc-web-sdk#production-lxp-v2025.2.1.0", "filestack-js": "^3.39.5", "foundation-sites": "^6.8.1", "marked": "^11.1.1", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "polished": "^4.3.1", "rc-time-picker": "^3.7.3", "react": "^18.2.0", "react-accessible-treeview": "^2.9.1", "react-bootstrap-daterangepicker": "^8.0.0", "react-datepicker": "^4.25.0", "react-dom": "^18.2.0", "react-focus-lock": "^2.9.6", "react-infinite-scroll-hook": "^4.1.1", "react-loading-skeleton": "^3.3.1", "react-pdf": "^9.2.1", "react-redux": "^8.0.5", "react-router-dom": "^6.22.0", "react-select": "^5.8.0", "screenfull": "^6.0.2"}}