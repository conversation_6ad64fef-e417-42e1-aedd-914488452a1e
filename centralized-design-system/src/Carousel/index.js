import React, { useState, useEffect, useRef } from 'react';
import { node, string, number, bool, arrayOf, oneOfType, func } from 'prop-types';

import { translatr } from '../Translatr/utils';
import { isRtl } from '../Utils/rtl';
import './styles.scss';

const Carousel = ({
  children,
  className = '',
  addScroll = 0,
  itemsContainerClassName = '',
  itemsContainerId = '',
  ariaLabelPrefix,
  ariaLabel = ariaLabelPrefix
    ? `${ariaLabelPrefix}, ${translatr('cds.common.main', 'Carousel')}`
    : translatr('cds.common.main', 'Carousel'), // default ariaLabel
  onNextBtnClickCB,
  onPrevBtnClickCB,
  role,
  scrollToLastVisible = false
}) => {
  const wrapperRef = useRef();
  const carouselRef = useRef();
  const prevButtonRef = useRef();
  const nextButtonRef = useRef();

  const [scrollLeft, setScrollLeft] = useState(undefined);
  const [scrollRight, setScrollRight] = useState(undefined);
  const [isCarouselItemsLoaded, setIsCarouselItemsLoaded] = useState(false);

  // This useEffect will executed after carousel items loaded
  useEffect(() => {
    // Scroll carousel to extreme left on Mount
    setTimeout(() => {
      //Add scroll listener here to avoid execution of scrollListener before carousel scrolled to Left
      carouselRef.current?.addEventListener('scroll', scrollListener);
      if (carouselRef.current) {
        carouselRef.current.scrollLeft = 0;
        checkForRightScrollable();
      }
    }, 500);

    return () => {
      carouselRef.current?.removeEventListener('scroll', scrollListener);
    };
  }, [isCarouselItemsLoaded]);

  // This useEffect will executed when number of carousel cards get's increased/decreased in real time
  useEffect(() => {
    // Set carouselItemsLoaded true when get children?.length > 0
    if (children?.length > 0 && !isCarouselItemsLoaded) {
      setIsCarouselItemsLoaded(true);
    }
    // Reseting state isCarouselItemsLoaded when number of cards decreased to zero
    if (children?.length === 0 && isCarouselItemsLoaded) {
      setIsCarouselItemsLoaded(false);
    }
    // check for rightScrollable when number of carousel cards get's increased/decreased in real time
    if (children.length) {
      checkForRightScrollable();
    }
  }, [children?.length]);

  useEffect(() => {
    const container = wrapperRef.current;
    if (!container) return;

    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          const isVisible = entry.isIntersecting;
          const item = entry.target;

          // Set aria-hidden attribute
          item.setAttribute('aria-hidden', isVisible ? 'false' : 'true');

          // Also update tabIndex for all focusable elements within the item
          const focusableElements = item.querySelectorAll(
            'a[href], button, input, textarea, select, details, [tabindex]:not([tabindex="-1"])'
          );

          focusableElements.forEach(el => {
            if (isVisible) {
              // Restore original tabIndex or remove -1
              if (el.dataset.originalTabIndex) {
                el.setAttribute('tabindex', el.dataset.originalTabIndex);
              } else if (el.getAttribute('tabindex') === '-1') {
                el.removeAttribute('tabindex');
              }
            } else {
              // Make not focusable
              el.setAttribute('tabindex', '-1');
            }
          });
        });
      },
      {
        root: container,
        threshold: 0.5
      }
    );

    const items = container.querySelectorAll('.ed-carousel > li');
    items.forEach(el => observer.observe(el));

    return () => items.forEach(el => observer.unobserve(el));
  }, []);

  useEffect(() => {
    if (scrollLeft === false) {
      nextButtonRef.current.focus();
    } else if (scrollRight === false) {
      prevButtonRef.current.focus();
    }
  }, [scrollLeft, scrollRight]);

  function checkForRightScrollable() {
    // offsetParent is the carousel cards container with fixed offsetWidth which doesn't increases/decrease on increase/decrease of number of cards inside it
    // whereas offsetWidth of carouselRef or wrapperRef increases/decrease on increase/decrease of number of cards inside it
    const canScrollRight =
      carouselRef.current?.scrollWidth -
      carouselRef.current?.offsetParent?.offsetWidth -
      carouselRef.current?.scrollLeft;
    // Added this work arround to show right scroll arrow only when more than 2px width is remaining
    if (canScrollRight > 2) {
      setScrollRight(true);
    } else {
      setScrollRight(false);
    }
  }

  function scrollListener() {
    // offsetParent is the carousel cards container with fixed offsetWidth which doesn't increases/decrease on increase/decrease of number of cards inside it
    // whereas offsetWidth of carouselRef or wrapperRef increases/decrease on increase/decrease of number of cards inside it
    let posLeft = carouselRef.current.scrollLeft || 0;
    let posRight =
      carouselRef.current.scrollWidth -
      carouselRef.current.offsetParent.offsetWidth -
      carouselRef.current.scrollLeft;
    if (!isRtl) {
      // Added this work arround to show left scroll arrow only when more than 2px width is remaining
      if (posLeft > 2) {
        setScrollLeft(true);
      } else {
        setScrollLeft(false);
      }

      if (posRight === 0) {
        setScrollRight(false);
      } else {
        checkForRightScrollable();
      }
    } else {
      if (posLeft >= 0) {
        setScrollLeft(false);
      } else {
        setScrollLeft(true);
      }

      if (
        carouselRef.current.scrollWidth -
          carouselRef.current.offsetWidth +
          carouselRef.current.scrollLeft >=
        2
      ) {
        setScrollRight(true);
      } else {
        setScrollRight(false);
      }
    }
  }

  function scrollToLeft() {
    const scrollAmount = wrapperRef.current.offsetWidth;

    carouselRef.current.scrollBy({
      top: 0,
      left: -(scrollAmount + addScroll), //use addScroll when content is cutting out of the offsetWidth
      behavior: 'smooth'
    });

    onPrevBtnClickCB && onPrevBtnClickCB();
  }

  function scrollToRight() {
    const scrollAmount = wrapperRef.current.offsetWidth;

    carouselRef.current.scrollBy({
      top: 0,
      left: scrollAmount + addScroll, //use addScroll when content is cutting out of the offsetWidth
      behavior: 'smooth'
    });

    onNextBtnClickCB && onNextBtnClickCB();
  }

  function scrollToLastVisibleOnRight() {
    const lastVisible = Array.from(carouselRef.current.children).find(child => {
      return (
        child.offsetLeft + child.offsetWidth >
        carouselRef.current.scrollLeft + carouselRef.current.offsetWidth
      );
    });

    carouselRef.current.scrollTo({
      top: 0,
      left: lastVisible.offsetLeft + addScroll,
      behavior: 'smooth'
    });
  }

  function scrollToLastVisibleOnLeft() {
    const lastVisible = Array.from(carouselRef.current.children).find(child => {
      return child.offsetLeft < carouselRef.current.scrollLeft;
    });

    carouselRef.current.scrollTo({
      top: 0,
      left:
        lastVisible.offsetLeft +
        lastVisible.offsetWidth -
        carouselRef.current.offsetWidth +
        addScroll,
      behavior: 'smooth'
    });
  }

  return (
    <div
      className={`ed-carousel-container ${scrollLeft ? 'left-scroll' : ''} ${
        scrollRight ? 'right-scroll' : ''
      } ${className}`}
      role="region"
      aria-label={ariaLabel}
    >
      <div ref={wrapperRef} className="ed-carousel-wrapper">
        <ul
          ref={carouselRef}
          className={`ed-carousel ${itemsContainerClassName}`}
          id={itemsContainerId}
          aria-live="polite"
          role={role}
        >
          {children}
        </ul>
      </div>
      <button
        ref={prevButtonRef}
        aria-label={
          ariaLabelPrefix
            ? `${translatr('cds.common.main', 'Previous')} ${ariaLabelPrefix}`
            : translatr('cds.common.main', 'Previous')
        }
        onClick={isRtl ? scrollToRight : scrollToLeft}
        className="scroll-btn left"
      >
        <span className="icon-angle-left-arrow leftArrowRTL" />
      </button>
      <button
        ref={nextButtonRef}
        aria-label={
          ariaLabelPrefix
            ? `${translatr('cds.common.main', 'Next')} ${ariaLabelPrefix}`
            : translatr('cds.common.main', 'Next')
        }
        onClick={
          isRtl
            ? scrollToLastVisible
              ? scrollToLastVisibleOnLeft
              : scrollToLeft
            : scrollToLastVisible
            ? scrollToLastVisibleOnRight
            : scrollToRight
        }
        className="scroll-btn right"
      >
        <span className="icon-angle-right-arrow rightArrowRTL" />
      </button>
    </div>
  );
};

Carousel.propTypes = {
  children: oneOfType([arrayOf(node), node, bool]),
  className: string,
  addScroll: number,
  itemsContainerClassName: string,
  itemsContainerId: string,
  ariaLabelPrefix: string,
  ariaLabel: string,
  onNextBtnClickCB: func,
  onPrevBtnClickCB: func,
  role: string,
  scrollToLastVisible: bool
};
export default Carousel;
