@import '../Styles/variables';

.ed-ui {
  .asterisk {
    color: var(--ed-input-asterisk-color);
  }

  .optional-text {
    color: var(--ed-text-color-primary);
  }

  .asterisk,
  .optional-text {
    padding-left: rem-calc(3);
    font-style: italic;
  }
}

.multilang-accordion-item {
  .ed-input-container {
    .ckeditor-placeholder {
      @include max-screen-width(768px) {
        top: 11.575rem;
      }
      @include max-screen-width(735px) {
        top: 13.575rem;
      }
      @include max-screen-width(628px) {
        top: 15.635rem;
      }
      @include max-screen-width(621px) {
        top: 17.8rem;
      }
      @include max-screen-width(479px) {
        top: 19.235rem;
      }
      @include max-screen-width(419px) {
        top: 21.3rem;
      }

      @include max-screen-width(391px) {
        top: 22.9rem;
      }

      @include max-screen-width(388px) {
        top: 24.3rem;
      }

      @include max-screen-width(362px) {
        top: 26.3rem;
      }
      @include max-screen-width(325px) {
        top: 29.5rem;
      }

      &.inline {
        @include max-screen-width(405px) {
          top: 5rem;
        }
      }
    }
  }
}
.ed-input-container {
  display: block;
  max-width: $ed-input-width;

  margin: 0;
  line-height: var(--ed-line-height-base);
  .css-t3ipsp-control {
    border-color: var(--ed-state-active-color);
    box-shadow: none;
  }

  .ckeditor-placeholder {
    position: absolute;
    top: rem-calc(218);
    left: rem-calc(22);
    color: var(--ed-text-color-supporting);

    @include max-screen-width(768px) {
      top: rem-calc(185);
    }

    @include max-screen-width(673px) {
      top: rem-calc(218);
    }

    @include max-screen-width(620px) {
      top: rem-calc(251);
    }

    @include max-screen-width(591px) {
      top: rem-calc(284);
    }

    @include max-screen-width(526px) {
      top: rem-calc(279);
    }

    @include max-screen-width(445px) {
      top: rem-calc(307);
    }

    @include max-screen-width(385px) {
      top: rem-calc(340);
    }

    @include max-screen-width(354px) {
      top: rem-calc(420);
    }

    &.inline {
      top: rem-calc(30);
      left: rem-calc(12);

      @include max-screen-width(371px) {
        top: 2rem;
      }

      &.placeholder-postion {
        top: rem-calc(60);

        @include max-screen-width(371px) {
          top: 5rem;
        }
      }
    }
  }
  ~ .ed-input-container {
    margin-top: var(--ed-spacing-base);
  }
  .characters-limit-msg {
    color: var(--ed-text-color-supporting);
    margin-bottom: var(--ed-spacing-2xs);
    float: right;
  }

  &.input-error {
    .ed-input-title {
      color: var(--ed-input-label-error-color);
    }
  }

  &.ed-input-with-attached-button {
    max-width: 100%;

    .input-group {
      .input-field {
        border-right: 0;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
      .input-field-button {
        color: var(--ed-text-color-primary);
        font-size: var(--ed-input-font-size);
        border-color: var(--ed-input-border-color);
        padding-left: var(--ed-spacing-sm);
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        white-space: nowrap;

        &:hover,
        &:focus {
          background-color: var(--ed-input-hover-bg-color);
          border: var(--ed-input-border-size) solid var(--ed-input-border-color);
        }
      }
    }
  }

  .ed-input-title {
    color: var(--ed-input-label-font-color);
    font-size: var(--ed-input-font-size);
    font-weight: var(--ed-input-label-font-weight);
    display: inline-flex;

    em {
      padding-left: 0.1875rem;
    }
  }

  .required-text {
    color: var(--ed-gray-4);
  }

  .ed-multi-select__control {
    border-radius: var(--ed-progress-bar-border-radius);

    .ed-multi-select__indicators {
      .clear-indicator-btn {
        position: relative;
        right: var(--ed-spacing-2xs);
      }
    }

    &.ed-multi-select__control--is-focused {
      border: var(--ed-input-border-size) solid var(--ed-state-active-color);
      box-shadow: none;
    }
    &::placeholder {
      color: var(--ed-text-color-supporting);
    }

    .ed-multi-select__input input {
      line-height: 1;
    }

    .ed-multi-select__placeholder {
      color: var(--ed-text-color-supporting);
    }

    .ed-multi-select__multi-value {
      border: var(--ed-tag-border-size) solid var(--ed-border-color);
      border-radius: var(--ed-tag-border-radius);
      background-color: var(--ed-tag-bg-color);
      font-size: var(--ed-tag-font-size);
      font-weight: var(--ed-tag-font-weight);
      line-height: var(--ed-line-height-sm);
      color: var(--ed-tag-font-color);
      padding: var(--ed-tag-padding-y) var(--ed-tag-padding-x);

      &__remove {
        &:hover {
          background-color: transparent;
          color: var(--ed-text-color-error);
        }

        svg {
          height: rem-calc(20);
          width: rem-calc(20);
        }
      }
    }
  }

  .multi-select-error-border__control {
    border: var(--ed-input-error-border-size) solid var(--ed-input-error-border-color);
    box-shadow: none;
  }

  .multi-select-error-border.input-error-border-container {
    border: none;

    &__control {
      border: rem-calc(1) solid var(--ed-negative-1);
    }
  }

  .ed-multi-select-transparent__control {
    border-radius: var(--ed-progress-bar-border-radius);

    &.ed-multi-select-transparent__control--is-focused {
      border: var(--ed-input-border-size) solid var(--ed-state-active-color);
      box-shadow: none;
    }
    &::placeholder {
      color: var(--ed-text-color-supporting);
    }

    .ed-multi-select-transparent__input input {
      line-height: 1;
    }
    .ed-multi-select-transparent__multi-value {
      border-radius: rem-calc(50);
      background: var(--ed-body-bg-color);
      border: var(--ed-input-border-size) solid var(--ed-input-disabled-border-color);
    }
    .ed-multi-select-transparent__multi-value__remove {
      border: var(--ed-input-border-size) solid var(--ed-state-disabled-color);
      border-radius: var(--ed-border-radius-circle);
      margin: var(--ed-spacing-4xs) var(--ed-spacing-3xs);
      padding: rem-calc(1);
      &:hover {
        color: $fixed-negative-color;
        border: var(--ed-input-border-size) solid $fixed-negative-color;
        background: var(--ed-body-bg-color);
      }
    }
  }

  .ed-select-error-title {
    color: var(--ed-select-error-color);
  }

  .input-group {
    position: relative;

    .input-field-icon {
      position: absolute;
      z-index: 2;
      display: block;
      font-size: var(--ed-input-font-size);
      line-height: var(--ed-line-height-base);
      padding-top: var(--ed-spacing-2xs);
      padding-right: var(--ed-spacing-sm);
      cursor: pointer;
      color: var(--ed-text-color-error);
      right: 0;
      top: 0;
    }
    .search {
      color: var(--ed-text-color-supporting);
      padding-left: var(--ed-spacing-sm);
      border-left: var(--ed-input-border-size) solid var(--ed-input-border-color);
    }

    input,
    .ed-multi-select__control,
    .ed-multi-select-transparent__control {
      font-size: var(--ed-input-font-size) !important;
      color: var(--ed-input-font-color);
      line-height: var(--ed-line-height-base);
    }

    .input-field {
      width: 100%;
      min-height: var(--ed-input-height);
      padding: var(--ed-input-padding-y) var(--ed-input-padding-x);
      border-radius: var(--ed-progress-bar-border-radius);
      box-shadow: var(--ed-input-shadow);

      &.charc-overlap {
        padding-right: 2.6rem;
      }

      &:focus {
        outline: none !important;
        border: var(--ed-input-border-size) solid var(--ed-state-active-color);
      }

      &::placeholder {
        color: var(--ed-text-color-supporting) !important;
        opacity: 1; /* Firefox */
      }

      &:disabled {
        color: var(--ed-input-disabled-font-color);
        border-width: var(--ed-input-disabled-border-size);
        border-color: var(--ed-input-disabled-border-color);
        background-color: var(--ed-input-disabled-bg-color);
        box-shadow: none;
      }

      &:-webkit-autofill {
        -webkit-text-fill-color: var(--ed-input-font-color);
      }

      &.with-arrows {
        width: rem-calc(120);
      }
      &.input-error-border {
        padding-right: rem-calc(25);
      }
    }
  }

  .input-info {
    color: var(--ed-text-color-supporting);
    margin-bottom: var(--ed-spacing-2xs);
  }
  .input-hint {
    font-size: var(--ed-font-size-supporting);
  }
  .input-error {
    color: var(--ed-text-color-error);
    font-size: var(--ed-font-size-supporting);
  }
  .input-warning-message {
    color: var(--ed-text-color-warning);

    .warning-icon {
      padding-right: var(--ed-spacing-xs);
    }
  }
  .input-error-border,
  .input-error-border-container {
    border-radius: var(--ed-progress-bar-border-radius);
    border: var(--ed-input-error-border-size) solid var(--ed-input-error-border-color);
    &:focus {
      border: var(--ed-input-error-border-size) solid var(--ed-input-error-border-color);
    }
  }

  .ed-multi-select__control,
  .ed-multi-select-transparent__control {
    border: var(--ed-input-border-size) solid var(--ed-input-border-color);
    &:focus {
      border: var(--ed-input-border-size) solid var(--ed-state-active-color);
    }
  }

  .input-field-border {
    border: var(--ed-input-border-size) solid var(--ed-input-border-color);
    &:focus,
    &:hover {
      border: var(--ed-input-border-size) solid var(--ed-state-active-color);
    }
  }

  .input-area {
    width: 100%;
    resize: none;
    max-height: rem-calc(120);
    box-shadow: var(--ed-input-shadow);
    border-radius: var(--ed-progress-bar-border-radius);
    padding: var(--ed-spacing-xs) var(--ed-spacing-sm) 0 var(--ed-spacing-sm);

    &:focus {
      outline: none;
    }

    &::placeholder {
      color: var(--ed-text-color-supporting);
    }
  }
  .input-area-info {
    color: var(--ed-text-color-supporting);
    margin-bottom: var(--ed-spacing-2xs);
    float: right;
  }
  &.pointer-events-none {
    // disabled state
    .ed-dropdown {
      color: var(--ed-input-disabled-font-color);
      border-color: var(--ed-input-disabled-border-color);
      background-color: var(--ed-input-disabled-bg-color);
    }
  }
  .ed-dropdown {
    display: inline-block;
    width: 100%;
    min-height: 0;
    border: var(--ed-input-border-size) solid var(--ed-input-border-color);
    border-radius: var(--ed-progress-bar-border-radius);

    .dd-label {
      margin-right: var(--ed-spacing-2xs);
    }

    .loader {
      float: right;
      margin: 0 var(--ed-spacing-4xs);
    }

    .dd-arrow {
      border: rem-calc(4) solid;
      border-color: var(--ed-neutral-1) var(--ed-neutral-1) transparent transparent;
      transform: rotate(135deg);
      margin: var(--ed-spacing-3xs) var(--ed-spacing-xs);
      float: right;

      &:hover {
        cursor: pointer;
      }

      &.disabled {
        border-color: var(--ed-input-disabled-border-color) var(--ed-input-disabled-border-color)
          transparent transparent;
      }
    }

    & .dropdown-placeholder {
      &.disabled {
        color: var(--ed-input-disabled-font-color);
      }
    }

    div:last-child {
      max-width: 90%;
    }
  }

  .ed-dropdown-active {
    border: var(--ed-input-border-size) solid var(--ed-state-active-color);
  }

  .dd-singleValue {
    text-align: left;
    padding: var(--ed-spacing-3xs) 0 var(--ed-spacing-3xs) var(--ed-spacing-lg);
  }

  .dd-multiValue {
    margin: 0;
  }

  .dd-list {
    position: relative;
    padding: var(--ed-dropdown-padding-y) var(--ed-dropdown-padding-x);
    margin: var(--ed-dropdown-menu-top-spacing) 0 0 0;
    width: 100%;
    max-height: rem-calc(240);
    overflow-y: auto;
    z-index: 99;
    background: var(--ed-body-bg-color);
    border: var(--ed-dropdown-border-size) solid var(--ed-input-border-color);
    border-top: 0;
    border-radius: var(--ed-button-border-radius);
    box-shadow: var(--ed-dropdown-shadow);

    li {
      list-style-type: none;
      margin: 0;

      &:hover :not(.selected) {
        cursor: pointer;
        background-color: var(--ed-dropdown-menu-focus-bg-color);
      }
      button {
        display: flex;
        justify-content: space-between;
        padding: var(--ed-dropdown-menu-padding-y) var(--ed-dropdown-menu-padding-x);
        border-radius: var(--ed-dropdown-menu-border-radius);
        width: 100%;
        text-align: left;
      }
      .selected {
        background-color: var(--ed-state-active-color);
        color: var(--ed-body-bg-color);
      }
    }
  }

  .ed-multi-select__menu-notice--no-options,
  .ed-multi-select__menu-notice--loading {
    color: var(--ed-text-color-supporting) !important;
  }

  .ed-multi-select__option--is-focused,
  .ed-multi-select__option--is-selected {
    background: var(--ed-dropdown-menu-focus-bg-color);
    border: var(--ed-border-size-sm) solid var(--ed-dropdown-menu-focus-border-color);
    color: var(--ed-black);

    &:active {
      background: var(--ed-dropdown-menu-focus-bg-color);
      color: var(--ed-black);
    }
  }

  .ed-multi-select__multi-value__remove {
    padding: 0;

    i:before {
      font-size: var(--ed-tag-font-size);
    }
  }
  .multi-select-dropdown-sr-instructions {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
}

.ed-input-container.wcag .ed-multi-select.__placeholder {
  color: var(--ed-text-color-supporting);
}

.ed-search {
  max-width: 100%;
}

.progress-container {
  width: 100%;
  height: 50%;
  border: none;
  display: flex;

  background-color: inherit;

  &.progress-bar-size-small {
    progress {
      height: rem-calc(4);
    }
  }
  &.progress-bar-size-medium {
    progress {
      height: rem-calc(8);
    }
  }

  .input-img img {
    max-width: rem-calc(180);
    max-height: rem-calc(180);
  }

  progress {
    width: 100%;
    border-radius: var(--ed-progress-bar-border-radius);
    border: var(--ed-progress-bar-border-size) solid var(--ed-state-disabled-color);
    background-color: var(--ed-progress-bar-bg-color);
  }

  progress::-webkit-progress-bar {
    background-color: var(--ed-progress-bar-bg-color);
    background-color: var(--ed-progress-bar-bg-color);
    border-radius: var(--ed-progress-bar-border-radius);
  }

  progress::-webkit-progress-value {
    background-color: var(--ed-state-active-color);
    border-radius: var(--ed-progress-bar-border-radius);
  }
  progress::-moz-progress-bar {
    background-color: var(--ed-state-active-color);
    border-radius: var(--ed-progress-bar-border-radius);
  }
}

.rc-time-picker {
  display: inline-block;
  box-sizing: border-box;
}
.rc-time-picker-input {
  width: 100%;
  position: relative;
  display: inline-block;
  padding: var(--ed-spacing-3xs) var(--ed-input-padding-x);
  height: rem-calc(38);
  cursor: text;
  line-height: var(--ed-line-height-base);
  color: var(--ed-text-color-primary);
  background-color: var(--ed-body-bg-color);
  border: var(--ed-input-border-size) solid var(--ed-input-border-color);
  border-radius: var(--ed-progress-bar-border-radius);

  input[type='text'] {
    font-size: var(
      --ed-input-font-size
    ) !important; // main css file has all inputs with 14px !important
  }
}
.rc-time-picker-clear-icon {
  border: none;
  display: none;
}
.rc-time-picker-panel {
  z-index: 3000; // Modal is now set at 2000
  width: rem-calc(180);
  position: absolute;
  box-sizing: border-box;
}

.rc-time-picker-panel-inner {
  display: inline-block;
  position: relative;
  outline: none;
  list-style: none;
  font-size: var(--ed-font-size-sm);
  text-align: left;
  background-color: var(--ed-body-bg-color);
  border: var(--ed-input-border-size) solid var(--ed-input-border-color);
  border-radius: var(--ed-progress-bar-border-radius);
  line-height: var(--ed-line-height-base);
}

.rc-time-picker-panel-input-wrap {
  box-sizing: border-box;
  position: relative;
  padding: var(--ed-spacing-3xs);
  border-bottom: var(--ed-input-border-size) solid var(--ed-input-border-color);
}

.rc-time-picker-panel-input {
  margin: 0;
  padding-left: var(--ed-spacing-2xs);
  width: 100%;
  cursor: auto;
  line-height: var(--ed-line-height-base);
  outline: none;

  border: none;
}
.rc-time-picker-panel-select {
  float: left;
  font-size: rem-calc(14);
  border: var(--ed-input-border-size) solid var(--ed-input-border-color);
  border-width: 0 var(--ed-input-border-size);
  width: 33.33%;
  max-height: rem-calc(255);
  overflow-y: auto;
  margin-left: -0.063rem;
  position: relative;

  &:first-child {
    border-left: 0;
    margin-left: 0;
  }
  &:last-child {
    border-right: 0;
  }
  ul {
    list-style: none;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    width: 100%;
  }
  li {
    list-style: none;
    margin: 0;
    padding: 0 0 0 var(--ed-spacing-base);
    width: 100%;
    height: rem-calc(32);
    line-height: calc(var(--ed-spacing-3xl) * 1rem);
    text-align: left;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
}
.rc-time-picker-panel-select-option-selected {
  background: var(--ed-state-active-color);
  color: var(--ed-body-bg-color);
}
.rc-time-picker-panel-select-active {
  overflow-y: auto;
}

.ed-select-bg {
  width: auto;
  background: var(--ed-select-bg-color);
}

.ed-select-label {
  margin-bottom: var(--ed-select-label-margin);
}

.ed-select {
  display: block;
  font-size: var(--ed-input-font-size);
  line-height: calc(var(--ed-spacing-xl) * 1rem);
  padding: 0.2em 2em 0.3em var(--ed-input-padding-x);
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  border: var(--ed-input-border-size) solid var(--ed-select-border-color);
  border-radius: var(--ed-progress-bar-border-radius);
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20height%3D%221024%22%20width%3D%22767.5%22%3E%3Cpath%20d%3D%22M0%20384l383.75%20383.75L767.5%20384H0z%22%2F%3E%3C%2Fsvg%3E');
  background-repeat: no-repeat, repeat;
  background-position: right 0.7em top 50%, 0 0;
  background-size: 0.65em auto, 100%;
  height: rem-calc(40);

  &::-ms-expand {
    display: none;
  }

  &:hover {
    border-color: var(--ed-input-hover-border-color);
  }
  &:focus {
    outline: auto;
    outline-offset: var(--ed-spacing-4xs);
    outline-color: var(--ed-primary-base);
    border: var(--ed-input-border-size) solid var(--ed-primary-base);
  }

  &.input-error-border {
    border-width: var(--ed-select-error-border-size);
  }

  &:disabled,
  &[aria-disabled='true'] {
    color: var(--ed-select-disabled-font-color);
    border-width: var(--ed-input-disabled-border-size);
    border-color: var(--ed-select-disabled-border-color);
    background-color: var(--ed-input-disabled-bg-color);
    background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20height%3D%221024%22%20width%3D%22767.5%22%3E%3Cpath%20d%3D%22M0%20384l383.75%20383.75L767.5%20384H0z%22%20fill%3D%22gray%22%2F%3E%3C%2Fsvg%3E');
  }

  option {
    font-weight: normal;
  }
}

.ed-group-select-desc {
  margin-top: var(--ed-select-label-margin);
}
.ed-group-select-label {
  color: var(--ed-input-label-font-color);
  font-size: var(--ed-input-font-size);
  font-weight: var(--ed-input-label-font-weight);
  margin-bottom: var(--ed-select-label-margin);
}
.ed-group-dropdown-indicator {
  font-size: var(--ed-font-size-lg);
  padding: var(--ed-spacing-2xs);
}
.ed-group-divider {
  border: none;
  border-top: 1px solid var(--ed-gray-2);
  margin: var(--ed-spacing-2xs) calc(var(--ed-spacing-2xs) * 2);
}

.ed-ui {
  .range-slider-bar {
    --seek-before-width: 30%;

    appearance: none;
    background: var(--ed-progress-bar-bg-color);
    border: var(--ed-progress-bar-border-size) solid var(--ed-progress-bar-border-color);
    border-radius: var(--ed-progrss-bar-border-radius);
    position: relative;
    width: 100%;
    height: rem-calc(5);
    outline: none;

    &:focus {
      outline: rem-calc(2) solid var(--ed-primary-base); // Uses theme primary color
      outline-offset: rem-calc(2); // Creates space between outline and element
    }

    &::-moz-focus-outer {
      border: 0;
    }

    /* range slider bar - chrome and safari */
    &::before {
      content: '';
      height: 100%;
      width: var(--seek-before-width);
      background-color: var(--ed-range-slider-indicator-color);
      border-top-left-radius: var(--ed-progress-bar-border-radius);
      border-bottom-left-radius: var(--ed-progress-bar-border-radius);
      position: absolute;
      top: 0;
      left: 0;
      z-index: 2;
      cursor: pointer;
    }

    /* range slider bar - firefox */
    &::-moz-range-progress {
      background-color: var(--ed-range-slider-indicator-color);
      border-top-left-radius: var(--ed-progress-bar-border-radius);
      border-bottom-left-radius: var(--ed-progress-bar-border-radius);
      height: 100%;
    }

    /* knobby - chrome and safari */
    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      height: rem-calc(15);
      width: rem-calc(15);
      border-radius: var(--ed-border-radius-circle);
      border: none;
      background-color: transparent;
      cursor: pointer;
      position: relative;
      margin: rem-calc(-2) 0 0 0;
      z-index: 3;
      box-sizing: border-box;
    }

    /* knobby while dragging - chrome and safari */
    &:active {
      &::-webkit-slider-thumb {
        transform: scale(1.2);
        background: transparent;
      }
    }

    /* knobby - firefox */
    &::-moz-range-thumb {
      height: rem-calc(15);
      width: rem-calc(15);
      border-radius: var(--ed-border-radius-circle);
      border: transparent;
      background-color: transparent;
      cursor: pointer;
      position: relative;
      z-index: 3;
      box-sizing: border-box;
    }

    /* knobby while dragging - firefox */
    &:active {
      &::-moz-range-thumb {
        transform: scale(1.2);
        background: transparent;
      }
    }
  }
  .dropdown-placeholder {
    text-align: center;
    display: block;
    font-size: var(--ed-font-size-supporting);
    color: var(--ed-text-color-primary);
  }

  .dropdown-container {
    position: relative;
    padding: var(--ed-spacing-xl) var(--ed-spacing-2xs) 0;
    margin: 0;
    width: 100%;
    z-index: 99;
    background: var(--ed-body-bg-color);
    border-radius: var(--ed-progress-bar-border-radius);
    border: var(--ed-input-border-size) solid var(--ed-input-border-color);

    &.padding-bottom {
      padding-bottom: rem-calc(14);
    }

    .dropdown-scrollable-container {
      max-height: rem-calc(340);
      overflow-y: auto;
    }

    label.checkbox {
      margin-bottom: rem-calc(9);
      max-width: calc(100% - 1rem); // 1rem is gap width, when label is very long
    }

    .icon-caret-right-arrow {
      padding-left: var(--ed-spacing-3xs);
    }

    .checkbox-section {
      margin-left: var(--ed-spacing-base);
      padding-top: var(--ed-spacing-4xs);
      padding-bottom: var(--ed-spacing-4xs);
    }

    .checkbox-section .checkbox-with-arrow {
      display: flex;
      align-items: center;
    }

    .checkbox-section .checkbox-with-arrow .checkbox {
      margin-bottom: 0;
    }

    .gap {
      width: 1rem;
      height: 1rem;
      display: inline-block;
    }

    .topics-msg {
      position: absolute;
      top: 0;
      right: rem-calc(7);

      &.error-text {
        color: var(--ed-text-color-error);
        font-size: var(--ed-font-size-supporting);
      }

      &.topics-selected-indicator {
        right: rem-calc(7);
      }

      &.max-topics-error {
        left: rem-calc(7);
      }
    }
  }

  .ed-dropdown {
    &.tree-structure {
      padding: 3px;
      min-height: rem-calc(38);
      position: relative;

      &.disabled {
        cursor: not-allowed;
        .dropdown-open-button {
          cursor: not-allowed;
        }
      }

      .scrollable-div {
        max-height: rem-calc(90);
        overflow-y: auto;
        margin-left: 0;
        padding-right: var(--ed-spacing-base);
        li {
          display: contents;
          line-height: var(--ed-line-height-base);
          .tag-tooltip {
            display: inline-block;
            max-width: 100%;
            .ed-tag-container {
              display: flex;
              max-width: 100%;
              align-items: center;
              .tag-name {
                overflow: hidden;
                overflow-wrap: break-word;
                white-space: normal;
                word-wrap: break-word;
              }
            }
          }
        }
      }

      .dropdown-placeholder {
        margin-top: rem-calc(2);
        text-align: left;
        display: block;
        font-size: var(--ed-input-font-size);
        color: var(--ed-text-color-supporting);
        margin-left: var(--ed-spacing-xs);
      }

      .dropdown-placeholder.wcag {
        color: var(--ed-text-color-supporting);
      }

      &.dd-singleValue {
        min-height: rem-calc(38);
      }

      .dropdown-open-button {
        position: absolute;
        right: 0;
        top: 0;
        height: 100%;
        width: rem-calc(43);
        text-align: center;
        background: var(--ed-border-color-light);
        border-left: var(--ed-input-border-size) solid var(--ed-input-border-color);
        padding-top: var(--ed-spacing-5xs);
        border-top-right-radius: var(--ed-progress-bar-border-radius);
        border-bottom-right-radius: var(--ed-progress-bar-border-radius);
        cursor: pointer;

        .icon-angle-down-arrow {
          font-size: rem-calc(32);
          color: var(--ed-neutral-5);
          height: rem-calc(38);
        }
      }
    }
  }
}

.ed-multi-input {
  display: flex;

  .ed-select-wrapper {
    flex: 0 0 rem-calc(116);
    margin-right: var(--ed-spacing-base);
    position: relative;

    > div {
      min-width: rem-calc(150);
    }

    &:after {
      content: attr(data-label);
      position: absolute;
      right: rem-calc(30);
      top: rem-calc(6);
      pointer-events: none;
      color: var(--ed-text-color-supporting);
    }
  }
}

// Icon Select used in Modals/CreateSmartcard/CardTypes/live.js
.ed-multi-select__menu {
  z-index: 90; // Ensure dropdown is above any other items

  .image-thumbnail {
    width: var(--ed-font-size-3xl);
    height: var(--ed-font-size-3xl);
    border-radius: var(--ed-border-radius-sm);
  }
  .user-list-item {
    display: flex;
    align-items: center;
  }
  .ed-avatar {
    width: var(--ed-font-size-4xl);
    height: var(--ed-font-size-4xl);
    margin: 0 var(--ed-spacing-xs) 0 0;
  }
  .custom-icon-size {
    font-size: var(--ed-font-size-lg);
    margin-right: var(--ed-spacing-sm);
  }
  .project-fallback-icon {
    font-size: var(--ed-font-size-xl);
    margin: 0 var(--ed-spacing-2xs) 0 var(--ed-spacing-5xs) !important;
  }
  .smaller-handle {
    font-size: var(--ed-font-size-sm);
  }
  .image-error-placeholder {
    display: flex;
    align-items: center;
    svg {
      width: var(--ed-font-size-3xl);
      height: var(--ed-font-size-3xl);
      margin-right: var(--ed-spacing-2xs);
    }
  }
}

.ed-icon-select__control {
  .ed-multi-select__control--is-focused,
  .ed-multi-select-transparent__control--is-focused {
    border-color: var(--ed-primary-base);
    box-shadow: none;
  }
  div svg {
    margin-left: var(--ed-spacing-2xs);
  }
}

.ed-icon-select__item div {
  display: flex;

  svg {
    background: var(--ed-white); // Always make svg backgrounds white
  }

  &.ed-multi-select__option--is-focused,
  &.ed-multi-select__option--is-selected,
  &.ed-multi-select-transparent__option--is-focused,
  &.ed-multi-select-transparent__option--is-selected {
    background: var(--ed-primary-base);
    color: var(--ed-text-color-primary-light);

    &:active {
      background: var(--ed-primary-base);
      color: var(--ed-text-color-primary-light);
    }
  }

  > *:first-child {
    margin-right: var(--ed-spacing-2xs);
  }
}

[dir] .ed-input-container {
  position: relative;

  .css-13cymwt-control,
  .css-1fhf3k1-control,
  .css-t3ipsp-control {
    border: var(--ed-input-border-size) solid var(--ed-input-border-color);
    padding: 0;
    padding-right: var(--ed-spacing-4xs);

    .ed-multi-select {
      &__multi-value,
      &.__multi-value {
        &__label {
          font-size: var(--ed-tag-font-size) !important;
          color: var(--ed-tag-font-color);
          font-weight: var(--ed-tag-font-weight);
          line-height: var(--ed-line-height-sm);
          padding: 0;
        }

        &__remove {
          svg {
            width: 1rem;
            height: 1rem;
          }
        }
      }

      &.__placeholder {
        margin: 0 var(--ed-spacing-5xs);
      }

      &.__value-container {
        padding: var(--ed-spacing-5xs) var(--ed-spacing-2xs);
      }
    }
  }
}

// Dropdown with checkbox
.dropdown-checkbox-container {
  .dropdown-container {
    padding: var(--ed-spacing-base) var(--ed-spacing-2xs) 0;
  }
}

.theme-plare {
  .ed-multi-select__control,
  .ed-multi-select__control.ed-multi-select__control--is-focused,
  .ed-multi-select__control.ed-multi-select-transparent__control--is-focused {
    box-shadow: var(--ed-input-shadow);
  }

  .ed-input-container {
    .ed-select-with-ai-assistant,
    .ed-multi-select {
      .__control {
        box-shadow: var(--ed-input-shadow);
        border: var(--ed-input-border-size) solid var(--ed-input-border-color);
      }

      .clear-indicator-btn {
        display: none;
      }

      .__multi-value__remove {
        margin-left: var(--ed-spacing-4xs);
        margin-right: var(--ed-spacing-4xs);
      }

      .__multi-value {
        border: var(--ed-tag-border-size) solid var(--ed-border-color);
        border-radius: var(--ed-tag-border-radius);
        background-color: var(--ed-tag-bg-color);
        padding: var(--ed-tag-padding-y) var(--ed-tag-padding-x);
      }

      .__multi-value__label {
        padding: 0;
        font-size: var(--ed-tag-font-size);
        color: var(--ed-tag-font-color);
        font-weight: var(--ed-tag-font-weight);
        line-height: var(--ed-line-height-sm);
      }

      .__control--is-focused {
        border: var(--ed-border-size-md) solid var(--ed-state-active-color);
      }
    }

    .ed-multi-select__control {
      box-shadow: var(--ed-input-shadow);
      border: var(--ed-input-border-size) solid var(--ed-input-border-color);
    }

    .ed-multi-select__control--is-focused {
      border: var(--ed-border-size-md) solid var(--ed-state-active-color);
    }

    .ed-multi-input.input-time {
      .ed-select-wrapper {
        select#hours,
        select#mins {
          background-color: var(--ed-select-bg-color);
        }
      }
    }

    .input-field.input-field-border:focus {
      border: var(--ed-border-size-md) solid var(--ed-state-active-color);
    }
  }
}
