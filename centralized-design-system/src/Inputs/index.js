import React, { forwardRef, useEffect, useRef, useState, useCallback, useId } from 'react';
import { string, func, bool, number, object, array, oneOfType, any } from 'prop-types';
import RCTimePicker from 'rc-time-picker';
import moment from 'moment';
import isEqual from 'lodash/isEqual';
import { tr } from 'edc-web-sdk/helpers/translations';
import cx from 'classnames';

import CircularSpinner from '../MUIComponents/common/CircularSpinner';
import { translatr } from '../Translatr';
import { MultiSelectWrapper } from './mutliselect';
import { SearchInputWrapper } from './search';
import { Tags } from '../Tags/index';
import Tooltip from '../Tooltip';
import generateRandomAlphaNumericString from '../Utils/generateRandomAlphaNumericString';
import { isRtl } from '../Utils/rtl';
import { MAX_LIMIT_WARNING_MSG } from '../messageConstants';
import remCalc from '../Utils/remCalc';
import { selectLevelPlaceholderOption } from '../Utils/proficiencyLevels';
import TitleDescription from './TitleDescription';

export { MultiSelectSearch } from './mutliselect';

const isAccessibilityEnabled = window.localStorage.getItem('accessibility') === 'true';

const DEFAULT_SELECT_CLASSNAME = 'ed-select';

export const AreaInput = forwardRef(
  (
    {
      title,
      setValue,
      id = '',
      description = '',
      placeholder = '',
      defaultTextInput = '',
      setIsValid = () => {},
      optional,
      shouldCheckForMaxChar = true,
      maxLen = 2000,
      rows = 4,
      errorMsg = '',
      name = null,
      required,
      isError = false,
      onChangeCB = () => {},
      onFocus,
      onClick,
      isTranslated = false,
      ariaLive,
      disabled
    },
    ref
  ) => {
    const [textInput, setTextInput] = useState(defaultTextInput);
    const [isMaxLimitReached, setIsMaxLimitReached] = useState(false);
    const isEditingStarted = useRef(false);

    const getCharsLeftMsg = text => {
      if (shouldCheckForMaxChar) {
        return translatr('cds.inputs.main', 'MaxInputMessage', {
          remainingText: maxLen - text.length,
          maxLen
        });
      }
    };
    const [charLeftMessage, setCharLeftMessage] = useState(getCharsLeftMsg(textInput));
    const [error, setError] = useState(isTranslated ? errorMsg : tr(errorMsg));

    useEffect(() => {
      setTextInput(defaultTextInput);
      setCharLeftMessage(shouldCheckForMaxChar && getCharsLeftMsg(defaultTextInput));
    }, [defaultTextInput]);

    useEffect(() => {
      if (!isEditingStarted.current) {
        return;
      }
      setLimitError(!shouldCheckForMaxChar || textInput.length < maxLen);
    }, [textInput]);

    useEffect(() => {
      setError(isTranslated ? errorMsg : tr(errorMsg));
    }, [errorMsg]);

    const onChange = e => {
      let inputValue = e.target.value;
      const scripTagRegExp = /<[^>]*script/;
      const isScriptPresent = scripTagRegExp.test(inputValue);

      if (!isEditingStarted.current) {
        isEditingStarted.current = true;
      }

      if (isScriptPresent) {
        setError(
          isTranslated
            ? translatr('cds.inputs.main', 'ScriptErrorMsg')
            : tr('Script tag is not allowed!')
        ); // file load issue
        setIsValid(false);
        return;
      }

      //set value truncated to max limit
      if (shouldCheckForMaxChar && maxLen) {
        inputValue = inputValue?.substring(0, maxLen);
      }
      setTextInput(inputValue);
      setValue(inputValue);
      setCharLeftMessage(shouldCheckForMaxChar && getCharsLeftMsg(inputValue));
      setLimitError();

      if (shouldCheckForMaxChar && inputValue?.length >= maxLen) {
        setCharLeftMessage(getCharsLeftMsg(textInput));
        setLimitError(false);
      }
      setIsValid(true);

      // Callback to perform other operations
      onChangeCB();
    };

    const setLimitError = (reset = true) => {
      if (reset) {
        setError('');
        setIsMaxLimitReached(false);
      } else {
        setError(MAX_LIMIT_WARNING_MSG());
        setIsMaxLimitReached(true);
      }
    };

    const getInputWarningOrErrorClass = () => {
      if (!!error || isError || isMaxLimitReached) {
        return 'input-error-border';
      }
      return 'input-field-border';
    };

    const onFocusHandler = e => {
      if (!isEditingStarted.current && shouldCheckForMaxChar && textInput.length >= maxLen) {
        setLimitError(false);
      }
      if (onFocus) {
        onFocus(e);
      }
    };

    let maxInputAriaLabel = '';
    if (shouldCheckForMaxChar && charLeftMessage) {
      maxInputAriaLabel = `${charLeftMessage.split(' ')[0]} ${translatr(
        'web.common.main',
        'CharactersRemaining'
      )}`;
    }

    return (
      <div
        className={cx('ed-input-container', {
          'input-error': !!error || !!isMaxLimitReached
        })}
        aria-live={ariaLive}
      >
        <TitleDescription
          title={isTranslated ? title : tr(title)}
          description={isTranslated ? description : tr(description)}
          optional={optional}
          required={required}
          controlId={id}
          id={`${id}-title-description`}
        />
        <textarea
          ref={ref}
          value={textInput}
          id={id}
          className={`input-area supporting-text font-size-xl ${getInputWarningOrErrorClass()}`}
          placeholder={isTranslated ? placeholder : tr(placeholder)}
          rows={rows}
          name={name}
          required={required}
          aria-describedby={`characters-limit-msg-${id}`}
          aria-labelledby={`${id}-title-description`}
          onChange={onChange}
          onFocus={onFocusHandler}
          onClick={onClick}
          disabled={disabled}
          aria-required={required}
        />

        {shouldCheckForMaxChar && (
          <label
            aria-label={maxInputAriaLabel}
            id={`characters-limit-msg-${id}`}
            aria-live="polite"
            className={cx(
              { 'input-error': !!error || !!isMaxLimitReached },
              'supporting-text characters-limit-msg'
            )}
          >
            <span aria-hidden={true}>{charLeftMessage}</span>
          </label>
        )}
        {!!error && (
          <span className={'input-error'} role="alert" aria-live="assertive">
            {error}
          </span>
        )}
      </div>
    );
  }
);
AreaInput.propTypes = {
  title: string,
  setValue: func,
  id: string,
  description: string,
  placeholder: string,
  defaultTextInput: string,
  setIsValid: func,
  optional: oneOfType([bool, string, number]),
  shouldCheckForMaxChar: bool,
  maxLen: number,
  rows: number,
  errorMsg: string,
  name: oneOfType([string, object]),
  required: bool,
  onChangeCB: func,
  isError: bool,
  onFocus: func,
  onClick: func,
  isTranslated: bool,
  ariaLive: string,
  disabled: bool
};
export const Select = forwardRef(
  (
    {
      id = '',
      title,
      items,
      description,
      onChange,
      defaultValue = null,
      disabled,
      optional,
      required = false,
      name,
      error,
      tooltipPosition = 'right',
      tooltipCardInlineCss,
      tooltipMessage,
      scrollTooltipRelativeElement,
      ariaLabel = '',
      translateDropDownOptions = true,
      isTranslated = false,
      ariaLabelledBy = '',
      labelHtmlFor = null,
      hint = '',
      ignorePlaceholderSelection = false,
      shouldFocusOnError = true
    },
    ref
  ) => {
    let [inputError, setInputError] = useState(error);
    const [selectedValue, setSelectedValue] = useState(defaultValue);
    const generatedId = useId();
    const placeholderOption = selectLevelPlaceholderOption() || { value: '' };

    useEffect(() => {
      setSelectedValue(defaultValue);
    }, [defaultValue]);

    function handleChange(event) {
      const val = event.target.value;
      let item = items.filter(i => (i.value || i.label) === val)[0];
      if (ignorePlaceholderSelection && item && item.value === placeholderOption.value) {
        // Reset the select element to the previous value
        event.target.value = selectedValue;
        return; // do not fire onChange if placeholder is selected
      }

      // Update our internal state with the new value
      setSelectedValue(val);
      setInputError('');
      setInputClassName(DEFAULT_SELECT_CLASSNAME);
      if (typeof onChange === 'function') {
        onChange(item, event);
      }
    }

    const [inputClassName, setInputClassName] = useState(DEFAULT_SELECT_CLASSNAME);
    useEffect(() => {
      setInputError(error);
      setInputClassName(`${inputClassName} ${error && 'input-error-border'}`);
    }, [error]);

    const selectId = id || `select-${generatedId}`;

    return (
      <div className="ed-input-container">
        {title && (
          <label
            htmlFor={selectId}
            className={`ed-input-title ed-select-label ${
              error && !disabled ? 'ed-select-error-title' : ''
            }`}
          >
            {isTranslated ? title : tr(title)}{' '}
            {(optional || required) &&
              (required ? (
                <span className="asterisk">*</span>
              ) : (
                <span className="optional-text">
                  {optional.toLowerCase() === 'optional'
                    ? translatr('cds.common.main', 'Optional')
                    : tr(optional)}
                </span>
              ))}
            {tooltipMessage && (
              <Tooltip
                message={tooltipMessage}
                pos={tooltipPosition}
                tooltipCardInlineCss={tooltipCardInlineCss}
                scrollTooltipRelativeElement={scrollTooltipRelativeElement}
                isTranslated
              >
                <button
                  aria-label={tooltipMessage}
                  tabIndex="0"
                  className="icon-info-circle radio-tooltip mt-5"
                />
              </Tooltip>
            )}
          </label>
        )}
        {description && (
          <label
            className="input-info supporting-text"
            id={ariaLabelledBy || `label-for-${selectId}`}
            {...(labelHtmlFor && { htmlFor: labelHtmlFor })}
          >
            {isTranslated ? description : tr(description)}
          </label>
        )}
        <div className="ed-select-bg">
          <select
            id={selectId}
            aria-label={ariaLabel ? (isTranslated ? ariaLabel : tr(ariaLabel)) : null}
            aria-labelledby={ariaLabelledBy === '' ? `label-for-${selectId}` : ariaLabelledBy}
            className={inputClassName}
            name={name}
            defaultValue={defaultValue}
            required={required}
            onChange={handleChange}
            disabled={disabled}
            ref={ref}
          >
            {items.map((item, i) => {
              return (
                <option
                  key={i}
                  value={item.value}
                  disabled={item.disabled}
                  selected={defaultValue !== '' && item.value === defaultValue}
                >
                  {translateDropDownOptions
                    ? tr(item.label || item.value)
                    : item.label || item.value}
                </option>
              );
            })}
          </select>
        </div>
        {hint && <div className="input-hint">{hint}</div>}
        {inputError && (
          <button
            className="input-error"
            aria-label={tr(inputError)}
            tabIndex={shouldFocusOnError ? 0 : -1}
          >
            {isTranslated ? inputError : tr(inputError)}
          </button>
        )}
      </div>
    );
  }
);
Select.propTypes = {
  id: string,
  title: string,
  items: array,
  description: string,
  onChange: func,
  defaultValue: oneOfType([string, number]),
  disabled: bool,
  optional: oneOfType([bool, string, number]),
  required: bool,
  name: oneOfType([string, object]),
  error: string,
  tooltipPosition: string,
  tooltipCardInlineCss: object,
  tooltipMessage: string,
  scrollTooltipRelativeElement: object,
  ariaLabel: string,
  translateDropDownOptions: bool,
  isTranslated: bool,
  ariaLabelledBy: string,
  labelHtmlFor: string,
  hint: string,
  ignorePlaceholderSelection: bool,
  shouldFocusOnError: bool
};

export const SelectMonth = ({
  title,
  description,
  onChange,
  defaultValue = null,
  disabled,
  startMonth = 1,
  endMonth = 12
}) => {
  let months = [
    { label: 'January', value: '01', id: 1 },
    { label: 'February', value: '02', id: 2 },
    { label: 'March', value: '03', id: 3 },
    { label: 'April', value: '04', id: 4 },
    { label: 'May', value: '05', id: 5 },
    { label: 'June', value: '06', id: 6 },
    { label: 'July', value: '07', id: 7 },
    { label: 'August', value: '08', id: 8 },
    { label: 'September', value: '09', id: 9 },
    { label: 'October', value: '10', id: 10 },
    { label: 'November', value: '11', id: 11 },
    { label: 'December', value: '12', id: 12 }
  ];
  const [items, setItems] = useState(months);
  useEffect(() => {
    setItems(months.filter(month => month.id >= startMonth && month.id <= endMonth));
  }, []);
  function handleChange(e) {
    const val = e.target.value;
    let item = items.filter(i => i.value === val)[0];
    if (typeof onChange === 'function') {
      onChange(item);
    }
  }

  return (
    <div className="ed-input-container">
      {title && <label className="ed-input-title">{tr(title)}</label>}
      {description && <label className="input-info supporting-text">{tr(description)}</label>}
      <select
        className="ed-select"
        defaultValue={defaultValue}
        onChange={handleChange}
        disabled={disabled}
      >
        <option>{translatr('cds.common.main', 'SelectMonth')}</option>
        {items.map(item => {
          return (
            <option key={item.id} value={item.value} disabled={item.disabled}>
              {item.label || item.value}
            </option>
          );
        })}
      </select>
    </div>
  );
};
SelectMonth.propTypes = {
  title: string,
  description: string,
  onChange: func,
  defaultValue: number,
  disabled: bool,
  startMonth: number,
  endMonth: number
};
export const SelectYear = ({
  title,
  startYear = parseInt(moment().format('YYYY')),
  endYear = startYear + 10,
  description,
  onChange,
  defaultValue = null,
  disabled,
  order
}) => {
  const [items, setItems] = useState([]);
  useEffect(() => {
    let years = [];
    for (let i = startYear; i <= endYear; ++i) {
      years.push({ label: `${i}`, value: `${i}`, id: i });
    }
    if (order === 'desc') {
      years?.sort((a, b) => (a.id > b.id ? -1 : 1));
    }
    setItems(years);
  }, []);
  function handleChange(event) {
    const val = event.target.value;
    let item = items.filter(i => i.value === val)[0];
    if (typeof onChange === 'function') {
      onChange(item);
    }
  }

  return (
    <div className="ed-input-container">
      {title && <label className="ed-input-title">{tr(title)}</label>}
      {description && <label className="input-info supporting-text">{tr(description)}</label>}
      <select
        className="ed-select"
        value={defaultValue}
        onChange={handleChange}
        disabled={disabled}
      >
        <option>{translatr('cds.common.main', 'SelectYear')}</option>
        {items.map(item => {
          return (
            <option key={item.id} value={item.value} disabled={item.disabled}>
              {item.label || item.value}
            </option>
          );
        })}
      </select>
    </div>
  );
};
SelectYear.propTypes = {
  title: string,
  startYear: number,
  endYear: number,
  description: string,
  onChange: func,
  defaultValue: number,
  disabled: bool,
  order: string
};
export const MultiSelect = ({
  id = '',
  title,
  items = [],
  description,
  onChange,
  disabled,
  optional,
  placeholder,
  required = false,
  error,
  creatableSelectClassName = 'ed-multi-select',
  tooltipMessage,
  scrollTooltipRelativeElement,
  isTranslated = false,
  renderGenAiAssistant = null
}) => {
  function handleChange(items1) {
    if (typeof onChange === 'function') {
      onChange(items1);
    }
  }

  const hasError = error && items.length <= 0;

  return (
    <div className={cx(`ed-input-container`, { wcag: isAccessibilityEnabled })} role="application">
      {title && (
        <label htmlFor={id} className="ed-input-title">
          {isTranslated ? title : tr(title)}{' '}
          {(optional || required) && (
            <span className="asterisk">
              {required
                ? '*'
                : optional.toLowerCase() === 'optional'
                ? translatr('cds.common.main', 'Optional')
                : tr(optional)}
            </span>
          )}
          {tooltipMessage && (
            <Tooltip
              message={tooltipMessage}
              pos={'right'}
              tooltipCardInlineCss={{
                'max-width': remCalc(450)
              }}
              scrollTooltipRelativeElement={scrollTooltipRelativeElement}
              isTranslated
            >
              <button aria-label={tooltipMessage} className="icon-info-circle radio-tooltip mt-5" />
            </Tooltip>
          )}
        </label>
      )}
      {description && (
        <label className="input-info supporting-text">
          {isTranslated ? description : tr(description)}
        </label>
      )}

      <MultiSelectWrapper
        id={id}
        creatableSelectClassName={`${creatableSelectClassName} ${
          hasError ? 'multi-select-error-border input-error-border-container' : ''
        }`}
        className="ed-select"
        values={items}
        onChange={handleChange}
        disabled={disabled}
        placeholder={placeholder}
        required={required}
      />
      {renderGenAiAssistant && renderGenAiAssistant()}

      {hasError && (
        <button className="input-error" tabIndex="0" aria-label={isTranslated ? error : tr(error)}>
          {isTranslated ? error : tr(error)}
        </button>
      )}
    </div>
  );
};
MultiSelect.propTypes = {
  id: string,
  title: string,
  items: array,
  description: string,
  onChange: func,
  disabled: bool,
  optional: oneOfType([bool, string, number]),
  required: bool,
  error: string,
  placeholder: string,
  creatableSelectClassName: string,
  tooltipMessage: string,
  scrollTooltipRelativeElement: object,
  isTranslated: bool,
  renderGenAiAssistant: func
};

export const AsyncSearchInput = forwardRef(
  (
    {
      id = '',
      title,
      items = [],
      description,
      onChange,
      clearInputOnSelect = false,
      disabled,
      optional,
      placeholder,
      skipCurrentUser = false,
      isClearable = false,
      ariaLabelledby = '',
      defaultAriaLabel = '',
      customAsyncSelectClass = '',
      disableAddingNew = false,
      hideTitle = false,
      fieldRequired = false,
      ...rest
    },
    ref
  ) => {
    function handleChange(value) {
      if (typeof onChange === 'function') {
        onChange(value);
      }
    }
    return (
      <div className="ed-input-container">
        {title && (
          <label
            htmlFor={id}
            id={`id-${id}`}
            className={cx('ed-input-title', {
              'sr-only': hideTitle
            })}
          >
            {title}
            {optional && (
              <span className="optional-text">
                {optional.toLowerCase() === 'optional'
                  ? translatr('cds.common.main', 'Optional')
                  : optional}
              </span>
            )}
          </label>
        )}
        {description && (
          <label htmlFor={id} id={`supporting-text-${id}`} className="input-info supporting-text">
            {description}
          </label>
        )}
        <SearchInputWrapper
          id={id}
          className="ed-select"
          customAsyncSelectClass={customAsyncSelectClass}
          clearInputOnSelect={clearInputOnSelect}
          values={items}
          onChange={handleChange}
          disabled={disabled}
          placeholder={placeholder}
          isClearable={isClearable}
          skipCurrentUser={skipCurrentUser}
          disableAddingNew={disableAddingNew}
          {...(ariaLabelledby !== ''
            ? { ariaLabelledby }
            : { ariaLabelledby: `supporting-text-${id}` })}
          {...(defaultAriaLabel == '' ? {} : { defaultAriaLabel })}
          required={fieldRequired}
          ref={ref}
          {...rest}
        />
      </div>
    );
  }
);
AsyncSearchInput.propTypes = {
  id: string,
  title: string,
  items: oneOfType([array, object]),
  description: string,
  onChange: func,
  clearInputOnSelect: bool,
  defaultValue: number,
  disabled: bool,
  optional: oneOfType([bool, string, number]),
  placeholder: string,
  isClearable: bool,
  skipCurrentUser: bool,
  ariaLabelledby: string,
  defaultAriaLabel: string,
  customAsyncSelectClass: string,
  disableAddingNew: bool,
  hideTitle: bool,
  fieldRequired: bool
};
export const ImageInput = ({
  title,
  description,
  onClick = () => {},
  defaultValue = null,
  optional,
  uploadText = 'Upload New File'
}) => {
  return (
    <div className="ed-input-container">
      {title && (
        <label className="ed-input-title">
          {tr(title)}
          {optional && (
            <span className="asterisk">
              {optional.toLowerCase() === 'optional'
                ? translatr('cds.common.main', 'Optional')
                : tr(optional)}
            </span>
          )}
        </label>
      )}
      {description && <label className="input-info supporting-text">{tr(description)}</label>}
      <div className="input-img">
        <img src={defaultValue} alt="uploaded file" />
      </div>
      <button className="ed-btn ed-btn ed-btn-neutral" onClick={onClick}>
        <i className="icon-upload" /> {tr(uploadText)}
      </button>
    </div>
  );
};
ImageInput.propTypes = {
  title: string,
  description: string,
  onClick: func,
  defaultValue: number,
  optional: oneOfType([bool, string, number]),
  uploadText: string
};
export const InputDropDown = props => {
  const {
    title,
    items,
    setSelected,
    description,
    defaultSelection,
    multiSelect,
    preLabel,
    placeholder,
    ariaDescribedby = '',
    disabled = false,
    loading = false,
    selected = [],
    tagsRemovedCB = () => {},
    id = '',
    ariaLabelledby = '',
    isTranslated = false,
    ariaLabel = ''
  } = props;
  const [open, setOpen] = useState(false);
  const [selection, setSelection] = useState(selected);
  const [options, setOptions] = useState([]);
  const [value, setValue] = useState('');
  const generatedId = useId();
  const dropdownId = id || `dropdown-${generatedId}`;
  const labelId = `${dropdownId}-label`;
  const listboxId = `${dropdownId}-listbox`;
  const listRef = useRef(null);

  useEffect(() => {
    if (isEqual(options, items)) {
      return;
    }
    setOptions(!items.length ? [] : [...items]);
  }, [items]);

  useEffect(() => {
    if (!defaultSelection) {
      return;
    }
    setSelection(!defaultSelection.length ? [] : [...defaultSelection]);
  }, [defaultSelection]);

  useEffect(() => {
    if (multiSelect) {
      if (selection.length === 0) {
        setValue(getSelectedItems());
        return;
      }
      const itemsNotSelected = items.filter(current => !isSelected(current));
      setOptions(itemsNotSelected);
    }
    setValue(getSelectedItems());
  }, [selection]);

  useEffect(() => {
    setSelection(selected);
  }, [selected.length]);

  useEffect(() => {
    setOpen(false);
    setValue(getSelectedItems());
  }, [disabled]);

  const handleSelection = item => {
    if (isSelected(item)) {
      return;
    }

    if (!multiSelect) {
      setSelection([item]);
      if (setSelected) {
        setSelected(item);
      }
    } else {
      setSelection([...selection, item]);
      if (setSelected) {
        setSelected([...selection, item]);
      }
    }
    setOpen(false);
  };

  const toggle = useCallback(
    e => {
      e.stopPropagation();
      if (!disabled) {
        setOpen(prevOpen => !prevOpen);
        setTimeout(() => {
          const listItems = Array.from(document.querySelectorAll('.dd-list-item button'));
          if (listItems.length > 0) {
            if (value === '') {
              listItems[0].focus(); // Focus on the first item if value is empty
            } else {
              const selectedItem = listItems.find(item => {
                return selection.some(sel => sel.id.toString() === item.dataset.id.toString());
              });
              if (selectedItem) {
                selectedItem.focus(); // Focus on the selected item
              } else {
                listItems[0].focus(); // Fallback: focus on the first item
              }
            }
          }
        }, 100);
      }
    },
    [disabled, selection, value]
  );

  const isSelected = item => {
    return [...selection].map(current => current.id).includes(item.id);
  };
  const isSelectedAfterRemove = (item, selectionAfterRemove) => {
    return [...selectionAfterRemove].map(current => current.id).includes(item.id);
  };

  const handleOnRemove = item => {
    let selectionAfterRemove = [...selection];
    selectionAfterRemove = selectionAfterRemove.filter(current => {
      if (current.id !== item) {
        return item;
      } else {
        null;
      }
    });
    const itemsNotSelected = items.filter(
      current => !isSelectedAfterRemove(current, selectionAfterRemove)
    );
    setOptions(itemsNotSelected);
    setSelection([...selectionAfterRemove]);
    setOpen(false);
    // Callback
    tagsRemovedCB({ selectionAfterRemove });
  };

  const handleArrowNavigationTags = useCallback((event, direction, currentIndex, listItems) => {
    event.preventDefault();
    const defaultIndex = 0;

    const targetIndex =
      currentIndex !== -1
        ? (currentIndex + direction + listItems.length) % listItems.length
        : defaultIndex;

    listItems[targetIndex]?.focus();
  }, []);

  const handleOnKeyDownTag = useCallback(
    e => {
      const escapedId = CSS.escape(`${dropdownId}-selected-value`);
      const listItems = Array.from(document.querySelectorAll(`#${escapedId} .ed-tag-container`));
      const current = document.activeElement;
      const currentIndex = listItems.indexOf(current);

      const removeValue = () => {
        if (currentIndex >= 0 && currentIndex < selection.length) {
          e.preventDefault();
          handleOnRemove(selection[currentIndex]?.id);
          if (currentIndex > 0) {
            listItems[currentIndex - 1]?.focus();
          } else if (listItems.length > 1) {
            listItems[0].focus();
          }
        }
      };

      const KEY_ACTIONS = {
        Backspace: removeValue,
        Delete: removeValue,
        ArrowDown: () => handleArrowNavigationTags(e, 1, currentIndex, listItems),
        ArrowRight: () => handleArrowNavigationTags(e, 1, currentIndex, listItems),
        ArrowUp: () => handleArrowNavigationTags(e, -1, currentIndex, listItems),
        ArrowLeft: () => handleArrowNavigationTags(e, -1, currentIndex, listItems)
      };

      const action = KEY_ACTIONS[e.key];
      if (action) action();
    },
    [dropdownId, selection, handleOnRemove, handleArrowNavigationTags]
  );

  function getSelectedItems() {
    if (!selection?.length || !Object.keys(selection[0]).length) {
      return (
        <>
          {placeholder ? (
            <span className={cx('dropdown-placeholder', { disabled: disabled })}>
              {placeholder}
            </span>
          ) : (
            translatr('cds.common.main', 'ChooseOption')
          )}
        </>
      );
    }

    if (!multiSelect) {
      return selection[0]?.label;
    } else {
      return selection.map(item => {
        return (
          <Tags
            id={item?.id}
            name={item?.value}
            cb={handleOnRemove}
            handleKeyDown={handleOnKeyDownTag}
            tabIndex={-1}
          />
        );
      });
    }
  }

  const handleArrowNavigationListItem = useCallback(
    (event, direction) => {
      event.preventDefault();
      const listItems = Array.from(listRef.current?.querySelectorAll('button') || []);
      const current = document.activeElement;
      const currentIndex = listItems.indexOf(current);

      const defaultIndex = options.findIndex(
        opt => opt.id === (selection[0]?.id || options[0]?.id)
      );

      const targetIndex =
        currentIndex !== -1
          ? (currentIndex + direction + listItems.length) % listItems.length
          : defaultIndex;

      listItems[targetIndex].focus();
    },
    [listRef, options, selection]
  );

  const handleOnKeyDownListItem = useCallback(
    (e, item) => {
      // If Tab or Shift+Tab is pressed, allow default browser focus navigation
      if (e.key === 'Tab') {
        return;
      }

      const KEY_ACTIONS = {
        Enter: () => {
          e.preventDefault();
          handleSelection(item);
        },
        ' ': () => {
          e.preventDefault();
          handleSelection(item);
        },
        Escape: () => {
          setOpen(false);
        },
        ArrowDown: () => handleArrowNavigationListItem(e, 1),
        ArrowUp: () => handleArrowNavigationListItem(e, -1)
      };

      const action = KEY_ACTIONS[e.key];
      if (action) action();
    },
    [handleSelection, handleArrowNavigationListItem]
  );

  const handleFocusOut = useCallback(
    e => {
      // Check if the next focused element is outside the dropdown
      if (!e?.currentTarget?.contains(e?.relatedTarget)) {
        setOpen(false);
      }
    },
    [setOpen]
  );

  return (
    <div
      className={cx('ed-input-container', {
        'pointer-events-none cursor-not-allowed-imp': disabled
      })}
    >
      {title && (
        <label id={labelId} className="ed-input-title">
          {isTranslated ? title : tr(title)}
        </label>
      )}
      {description && (
        <label className="input-info supporting-text">
          {isTranslated ? description : tr(description)}
        </label>
      )}
      <div
        aria-expanded={open}
        aria-controls={listboxId}
        className={cx('ed-dropdown supporting-text', {
          'cursor-not-allowed-imp': disabled,
          'dd-multiValue': multiSelect && selection.length,
          'dd-singleValue': !multiSelect || !selection.length,
          'ed-dropdown-active': open
        })}
        key="input-dd"
        onClick={toggle}
        onKeyDown={e => {
          if (['Enter', ' ', 'ArrowDown'].includes(e.key) && !disabled) {
            e.preventDefault();
            toggle(e);
          }
          if (e.key === 'Escape') {
            setOpen(false);
          }
          if (['ArrowRight', 'ArrowLeft']?.includes(e.key)) {
            handleOnKeyDownTag(e);
          }
        }}
        role={multiSelect ? 'combobox' : 'button'}
        aria-label={ariaLabel}
        tabIndex={disabled ? -1 : 0}
        aria-describedby={ariaDescribedby}
        aria-labelledby={`${labelId} ${dropdownId}-selected-value ${ariaLabelledby}`}
        id={dropdownId}
        aria-description={
          multiSelect
            ? translatr('cds.common.main', 'MultiValueComboBoxDesc', {
                selectedCount: selection.length,
                totalOptions: options.length
              })
            : undefined
        }
        aria-disabled={disabled}
      >
        {preLabel && <span className="dd-label">{isTranslated ? preLabel : tr(preLabel)}</span>}
        {loading ? (
          <CircularSpinner />
        ) : (
          <span className={cx('dd-arrow', { disabled: disabled })} />
        )}
        <span id={`${dropdownId}-selected-value`}>{value}</span>
      </div>
      {open && (
        <ul
          id={listboxId}
          className="dd-list"
          role="listbox"
          aria-labelledby={labelId}
          aria-multiselectable={multiSelect}
          onBlur={handleFocusOut}
          tabIndex={-1}
          ref={listRef}
        >
          {options.map(item => {
            const itemSelected = isSelected(item);
            return (
              <li className="dd-list-item" key={item.id} role="option" aria-selected={itemSelected}>
                <button
                  data-id={item.id}
                  type="button"
                  className={`${itemSelected ? 'selected' : ''}`}
                  onClick={() => handleSelection(item)}
                  tabIndex={-1}
                  onKeyDown={e => handleOnKeyDownListItem(e, item)}
                >
                  <span className={`${itemSelected ? 'selected' : ''} supporting-text`}>
                    {isTranslated ? item.label || item.value : tr(item.label || item.value)}
                  </span>
                </button>
              </li>
            );
          })}
        </ul>
      )}
    </div>
  );
};

InputDropDown.propTypes = {
  title: string,
  items: array,
  setSelected: func,
  description: string,
  defaultSelection: array,
  multiSelect: bool,
  preLabel: string,
  placeholder: string,
  disabled: bool,
  loading: bool,
  selected: array,
  tagsRemovedCB: func,
  ariaDescribedby: string,
  id: string,
  ariaLabel: string,
  ariaLabelledby: string,
  isTranslated: bool
};

export const SearchInput = forwardRef(
  (
    {
      placeholder,
      onSearch,
      onBlur,
      searchOnTextChange = true,
      ariaLabel,
      maxLen,
      value,
      id,
      ariaDescribedby,
      onTextChange = val => val,
      buttonAriaLabel
    },
    ref
  ) => {
    const [inputText, setInputText] = useState();
    const placeholderText = placeholder?.replace(/\./g, '') || 'Search Here';
    useEffect(() => {
      setInputText(value);
    }, [value]);

    const handleOnSearch = () => {
      if (onSearch && inputText?.trim()) {
        onSearch(inputText?.trim() || '', 'search');
      }
    };

    const handleOnKeydown = e => {
      if (e.keyCode === 13 && onSearch && inputText?.trim()) {
        onSearch(inputText?.trim() || '', 'search');
      }
    };

    const onChange = e => {
      const { value: newValue } = e.target;
      // Should not allow entering only space
      if (newValue?.trim() !== '' || inputText?.trim() !== '') {
        if (maxLen) {
          const croppedValue = newValue.slice(0, maxLen);
          setInputText(croppedValue);
          searchOnTextChange && onSearch(croppedValue || '');
        } else {
          setInputText(newValue);
          searchOnTextChange && onSearch(newValue || '');
        }
      }
      onTextChange(newValue);
    };

    const handleBlur = () => {
      if (onBlur) {
        onBlur();
      }
    };

    return (
      <div className="ed-input-container ed-search">
        <div className="input-group">
          <input
            ref={ref}
            id={id || ''}
            value={inputText}
            className="input-field input-field-border charc-overlap"
            type={'text'}
            placeholder={placeholder || translatr('cds.common.main', 'SearchHere')}
            onChange={onChange}
            onKeyDown={handleOnKeydown}
            aria-label={tr(
              ariaLabel ||
                `${placeholderText} : Start typing, then use the Tab key to select an option from the list`
            )}
            aria-describedby={ariaDescribedby}
            onBlur={handleBlur}
          />
          <button
            aria-label={buttonAriaLabel || translatr('cds.common.main', 'Search')}
            className="input-field-icon"
            onClick={handleOnSearch}
          >
            <i className="icon-search search" />
          </button>
        </div>
      </div>
    );
  }
);

SearchInput.propTypes = {
  placeholder: string,
  onSearch: func,
  onBlur: func,
  searchOnTextChange: bool,
  ariaLabel: string,
  maxLen: number,
  value: string,
  id: string,
  ariaDescribedby: string,
  onTextChange: func,
  buttonAriaLabel: string
};

export const TimePicker = ({
  time = moment(),
  cb = null,
  use12Hours = false,
  placeHolder = '',
  disabled,
  required = false
}) => {
  const [value, setValue] = useState(placeHolder ? null : time);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (isOpen) {
      const panel = document.querySelector('.rc-time-picker-panel');

      const handleKeyDown = e => {
        const activeList = document.activeElement.closest('ul');
        const items = activeList?.querySelectorAll('li');
        const currentIndex = Array.from(items || []).findIndex(
          item => item === document.activeElement
        );

        if (e.key === 'ArrowDown' && currentIndex < items.length - 1) {
          e.preventDefault();
          items[currentIndex + 1].focus();
          items[currentIndex + 1].click();
        }
        if (e.key === 'ArrowUp' && currentIndex > 0) {
          e.preventDefault();
          items[currentIndex - 1].focus();
          items[currentIndex - 1].click();
        }
        if (e.key === 'Tab') {
          e.preventDefault();
          const lists = document.querySelectorAll('.rc-time-picker-panel-select ul');
          const nextList = lists[Array.from(lists).indexOf(activeList) + 1];
          nextList?.querySelector('li')?.focus();
        }
      };

      panel?.addEventListener('keydown', handleKeyDown);
      document.querySelector('.rc-time-picker-panel-select ul li')?.focus();

      return () => panel?.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen]);

  const handleOnChange = value1 => {
    setValue(value1.clone());
    if (cb) {
      return cb(value1.clone());
    }
  };

  const getIcon = () => {
    return (
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        aria-label={translatr('cds.inputs.main', 'OpenTimePicker')}
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        style={isRtl ? { ...iconStyle, left: '0.375rem' } : { ...iconStyle, right: '0.375rem' }}
      >
        <i className="icon-clock" />
      </button>
    );
  };

  const getClearIcon = () => {
    return (
      <button
        className="rc-time-picker-clear"
        title={translatr('cds.common.main', 'Clear')}
        tabIndex="-1"
      >
        <i className="rc-time-picker-clear-icon" />
      </button>
    );
  };

  useEffect(() => {
    setValue(time.clone());
  }, [time]);

  const changeTimeLocale = ({ open }) => {
    if (open) {
      setTimeout(() => {
        const hoursSelect = document.querySelector('.rc-time-picker-panel-select:nth-child(1) ul');
        const minutesSelect = document.querySelector(
          '.rc-time-picker-panel-select:nth-child(2) ul'
        );
        const secondsSelect =
          !use12Hours && document.querySelector('.rc-time-picker-panel-select:nth-child(3) ul');

        if (hoursSelect) {
          hoursSelect.setAttribute('role', 'listbox');
          hoursSelect.setAttribute('aria-label', translatr('cds.common.main', 'Hours'));
          Array.from(hoursSelect.children).forEach(li => li.setAttribute('role', 'option'));
        }

        if (minutesSelect) {
          minutesSelect.setAttribute('role', 'listbox');
          minutesSelect.setAttribute('aria-label', translatr('cds.inputs.main', 'Minutes'));
          Array.from(minutesSelect.children).forEach(li => li.setAttribute('role', 'option'));
        }

        if (secondsSelect) {
          secondsSelect.setAttribute('role', 'listbox');
          secondsSelect.setAttribute('aria-label', translatr('cds.inputs.main', 'Seconds'));
          Array.from(secondsSelect.children).forEach(li => li.setAttribute('role', 'option'));
        }

        const AMPM_Row =
          use12Hours && document.querySelector('.rc-time-picker-panel-select:nth-child(3) ul');
        if (AMPM_Row) {
          AMPM_Row.setAttribute('role', 'listbox');
          AMPM_Row.setAttribute('aria-label', translatr('cds.inputs.main', 'AMPM'));
          Array.from(AMPM_Row.children).forEach(element => {
            element.innerText = tr(element.innerText);
            element.setAttribute('role', 'option');
          });
        }
      }, 50);
    }
  };

  const timePickerParentId = 'time-picker-parentid' + generateRandomAlphaNumericString();

  useEffect(() => {
    const timePickerInput = document.querySelector(`#${timePickerParentId} .rc-time-picker-input`);
    if (timePickerInput && required) {
      timePickerInput.setAttribute('aria-required', required);
    }
  }, []);

  return (
    <div id={timePickerParentId}>
      <RCTimePicker
        style={{ position: 'relative' }}
        value={value}
        format={use12Hours ? 'hh:mm A' : 'HH:mm:ss'}
        showSecond={!use12Hours}
        inputReadOnly={false}
        onChange={handleOnChange}
        inputIcon={getIcon()}
        use12Hours={use12Hours}
        placeholder={placeHolder}
        focusOnOpen
        disabled={disabled}
        clearIcon={getClearIcon()}
        onOpen={changeTimeLocale}
        open={isOpen}
        onClose={() => setIsOpen(false)}
        getPopupContainer={() => {
          return document.getElementById(timePickerParentId);
        }}
      />
    </div>
  );
};
TimePicker.propTypes = {
  use12Hours: bool,
  placeHolder: string,
  disabled: bool,
  cb: func,
  time: object,
  required: bool
};
const PROGRESSBAR_SIZES = {
  SMALL: 'small',
  MEDIUM: 'medium'
};
export const LinearProgressBar = ({
  value = 0,
  hideLabel = false,
  size = PROGRESSBAR_SIZES.MEDIUM,
  id = `linear-progressbar-${Math.random()
    .toString(36)
    .slice(2, 11)}`
}) => {
  const width = `${value}%`;
  const progressSize = Object.values(PROGRESSBAR_SIZES).includes(size)
    ? size
    : PROGRESSBAR_SIZES.MEDIUM;
  return (
    <div className={`progress-container progress-bar-size-${progressSize}`}>
      {!hideLabel && (
        <label
          htmlFor={id}
          className="progress-value"
          data-label={translatr('cds.common.main', 'Completed')}
          aria-hidden={true}
        >
          {width}
        </label>
      )}
      <progress id={id} max="100" value={value}>
        {width}
      </progress>
    </div>
  );
};
LinearProgressBar.propTypes = {
  value: number,
  hideLabel: bool,
  size: string,
  id: string
};
const iconStyle = {
  position: 'absolute',
  display: 'inline-block',
  lineHeight: '1.5',
  top: '0.25rem',
  border: 'none'
};

export const InputWithButton = React.forwardRef((props, ref) => {
  const {
    id,
    placeholder,
    onSubmitHandler,
    onKeyDownHandler,
    ariaLabelValue,
    isDisabled,
    onChangeHandler,
    buttonContent,
    label,
    description,
    defaultValue,
    isBtndisabled,
    buttonAriaLabel,
    isTranslated = false
  } = props;

  const generatedId = useId();
  const inputId = id || `input-${generatedId}`;
  const descriptionId = description ? `desc-${inputId}` : undefined;
  const labelId = label ? `label-${inputId}` : undefined;

  const inputPlaceholderLabel = isTranslated ? placeholder : tr(placeholder || 'Type here...');
  const handleOnKeydown = e => {
    if (e.key === 'Enter') {
      onSubmitHandler();
    }
    // Just in case if some custom operations are to be performed
    onKeyDownHandler?.(e);
  };

  const onChange = e => {
    onChangeHandler?.(e.target.value);
  };

  return (
    <div className="ed-input-container ed-input-with-attached-button">
      {label && (
        <label id={labelId} htmlFor={inputId} className="ed-input-title">
          {isTranslated ? label : tr(label)}
        </label>
      )}
      {description && (
        <label id={descriptionId} className="input-info supporting-text">
          {isTranslated ? description : tr(description)}
        </label>
      )}
      <div className="input-group flex">
        <input
          className="input-field input-field-border"
          type={'text'}
          value={defaultValue}
          placeholder={inputPlaceholderLabel}
          onChange={onChange}
          onKeyDown={handleOnKeydown}
          aria-label={(isTranslated ? ariaLabelValue : tr(ariaLabelValue)) || inputPlaceholderLabel}
          aria-describedby={`${labelId} ${descriptionId || ''}`.trim()}
          disabled={isDisabled}
          ref={ref}
          id={inputId}
          autoComplete="off"
        />

        <button
          aria-label={buttonAriaLabel}
          className="ed-btn ed-btn-neutral input-field-button"
          onClick={onSubmitHandler}
          disabled={isDisabled || isBtndisabled}
        >
          {isTranslated ? buttonContent : tr(buttonContent)}
        </button>
      </div>
    </div>
  );
});

InputWithButton.propTypes = {
  id: string,
  placeholder: string,
  onSubmitHandler: func.isRequired,
  onChangeHandler: func,
  onKeyDownHandler: func,
  ariaLabelValue: string,
  isDisabled: bool,
  ref: object,
  buttonContent: any.isRequired, // It can be icon, text or both
  label: string,
  description: string,
  defaultValue: oneOfType([string, number]),
  isBtndisabled: bool,
  buttonAriaLabel: string,
  isTranslated: bool
};
export const NumberInput = ({
  title = '',
  description = '',
  optional = true,
  min = 0,
  max = 100,
  classNames = '',
  defaultValue,
  setValue,
  id = '',
  error = ''
}) => {
  const [inputValue, setInputValue] = useState(defaultValue);
  const [isValid, setIsValid] = useState(true);
  const setChangedValue = value => {
    if (!value && optional) {
      setInputValue(value);
      setValue(value);
      setIsValid(true);
      return;
    }
    if (value < min || value > max) {
      setIsValid(false);
      setInputValue('');
      setValue('');
    } else {
      setInputValue(value);
      setValue(value);
      setIsValid(true);
    }
    if (value < min || value > max) {
      setIsValid(false);
    }
  };
  return (
    <div className="ed-input-container">
      <div className="input-group">
        {title && (
          <label htmlFor={id} className="ed-input-title">
            {title}
          </label>
        )}
        {optional && <i className="required-text">{translatr('cds.common.main', 'Optional')}</i>}
        {description && <label className="input-info supporting-text">{description}</label>}
        <input
          required={!optional}
          id={id}
          className={`input-field  with-arrows ${classNames} input-${
            isValid ? 'field' : 'error'
          }-border`}
          type="number"
          min={min}
          max={max}
          value={inputValue}
          onChange={e => setChangedValue(e.target.value)}
        />
        {!isValid && (
          <button
            className="input-error"
            aria-label={translatr('cds.common.main', 'ValueShouldBeBetweenMinAndMax', { min, max })}
            tabIndex="0"
          >
            {translatr('cds.common.main', 'ValueShouldBeBetweenMinAndMax', { min, max })}
          </button>
        )}
        {isValid && !!error && (
          <button className="input-error" aria-label={tr(error)} tabIndex="0">
            {tr(error)}
          </button>
        )}
      </div>
    </div>
  );
};

NumberInput.propTypes = {
  title: string,
  description: string,
  optional: oneOfType([bool, string, number]),
  min: number,
  max: number,
  classNames: string,
  id: string.isRequired,
  defaultValue: oneOfType([string, number]),
  setValue: func,
  error: string
};
