import React, { useState, useEffect, useCallback, forwardRef, useRef } from 'react';
import AsyncSelect from 'react-select/async';
import unescape from 'lodash/unescape';
import classNames from 'classnames';
import { searchGroups } from 'edc-web-sdk/requests/groups.v2';
import { getUsers, getTimeZones } from 'edc-web-sdk/requests/users.v2';
import { getChannelsMinimal, getSearchChannels } from 'edc-web-sdk/requests/channels.v2';
import { getQueryTopics, getSkillsLevels } from 'edc-web-sdk/requests/topics';
import { getRoles, getOmpRoles } from 'edc-web-sdk/requests/skills';
import { searchJobFamily } from 'edc-web-sdk/requests/extOpportunities';
import { search } from 'edc-web-sdk/requests/careerOportunities.v2';
import { getPincodeDetails } from 'edc-web-sdk/requests/fsOnboarding';
import { getLocationsForPreferences } from 'edc-web-sdk/requests/orgLocations';
import { NUMBER_REGEX } from 'edc-web-sdk/requests/validationConstants';
import { getOrganizations } from 'edc-web-sdk/requests/organizations.js';
import { fetchCardForTeamAndChannel } from 'edc-web-sdk/requests/cards.v2';
import { string, array, func, bool, object, any, oneOfType } from 'prop-types';

import { translatr } from '../Translatr';
import getSuggestions from '../Utils/getSuggestionsFromTaxonomy';
import debounce from '../Utils/debounce';
import { getConfig } from '../Utils/OrgConfigs';
import CustomMultiValueRemove from './CustomMultiValueRemove';
import Avatar from '../Avatar';
import ImageErrorPlaceHolderWithLabel from '../ImageErrorPlaceHolderWithLabel';

export const SearchInputWrapper = forwardRef(
  (
    {
      placeholder,
      multiselect = false,
      values = [],
      onChange = () => {},
      users = true,
      groups = true,
      channels = true,
      topics = false,
      skills = false,
      pincode = false,
      organizations = false,
      projects = false,
      mentorship = false,
      content = false,
      extraData = {},
      extraPayload = {},
      role = '',
      roles = false,
      locations = false,
      jobFamily = false,
      timezones = false,
      languages = false,
      exclude_user_ids = null,
      maxLimit = null,
      id = '',
      disabled = false,
      defaultInputValue = '',
      onClear = () => {},
      clearInputOnSelect = false,
      noOptionsMessage = null,
      isClearable = false,
      topicValueField = 'id',
      topicValueTranformFn = val => val,
      excludeSkillsIds = [],
      cacheOptions = true,
      skipCurrentUser,
      ariaLabelledby = '',
      defaultAriaLabel,
      customAsyncSelectClass = '',
      disableAddingNew = false,
      handleMenuClose,
      required = false
    },
    ref
  ) => {
    const [value, setValue] = useState(values);
    const [inputValue, setInputValue] = useState(defaultInputValue);
    const [ariaLabel] = useState(defaultAriaLabel || '');
    const currentUser = window.__ED__.id;
    const isJobRoleSelection = getConfig('hr_data_service_enablement', false);
    const inputRef = useRef(null);

    useEffect(() => {
      setValue(clearInputOnSelect ? null : values);
    }, [values?.length]);

    useEffect(() => {
      setInputValue(defaultInputValue);
    }, [defaultInputValue]);

    useEffect(() => {
      let clearButton = document.querySelector('.clear-indicator-btn');
      if (value?.label && clearButton)
        clearButton.ariaLabel = `${translatr('cds.common.main', 'Clear')} ${value.label}`;
    }, [value]);

    const _handleRequest = async (query, callback) => {
      const payload = {
        limit: 15,
        offset: 0,
        q: query,
        ...(extraData.userTeams && { user_teams: true })
      };
      let queries = [];
      if (users) {
        let userPayload = payload;
        if (role) {
          userPayload = { ...userPayload, role: role };
        }
        if (exclude_user_ids) {
          userPayload = { ...userPayload, exclude_user_ids: exclude_user_ids };
        }
        queries.push(
          getUsers({
            ...userPayload,
            fields: extraPayload.fields || 'id,full_name,handle,external_id',
            ...(extraPayload.hideSuspendedUsers && { include_suspended: false })
          })
        );
      }
      if (groups) {
        queries.push(
          searchGroups({
            ...payload,
            ...(extraData.userTeams && { writables: true }),
            ...(extraPayload.restrictPrivateContent && { restrictables: true }),
            ...(!extraPayload.showAllFields && { fields: 'id,name' }),
            ...(extraPayload.hidePrivateGroups && { is_private: false })
          })
        );
      }
      if (channels) {
        if (extraData.showingOnLandingPage) {
          queries.push(
            getSearchChannels({
              ...payload,
              ...extraPayload
            })
          );
        } else {
          queries.push(
            getChannelsMinimal({
              ...payload,
              ...(!extraPayload.showAllFields && { fields: 'id,label,is_private' }),
              ...(!extraData.showingOnLandingPage && { writables: true })
            })
          );
        }
      }
      if (topics) {
        const { currentUserLang } = extraData;
        const topicsPayload = {
          language: currentUserLang,
          query,
          ...extraPayload
        };
        queries.push(getQueryTopics(topicsPayload));
      }

      if (pincode) {
        const pincodePayload = {
          pincode: query,
          ...extraPayload
        };
        queries.push(getPincodeDetails(pincodePayload));
      }

      if (timezones) {
        queries.push(getTimeZones());
      }

      if (languages) {
        const langsConfig = global?.__edOrgData?.languages;
        const langsQuery = Object.keys(langsConfig)
          .map(lang => ({
            label: lang,
            value: langsConfig[lang]
          }))
          .filter(langObj => langObj?.label.toLowerCase().includes(query.toLowerCase()))
          .sort(
            (a, b) =>
              a.label.toLowerCase().indexOf(query.toLowerCase()) -
              b.label.toLowerCase().indexOf(query.toLowerCase())
          );
        queries.push(Promise.resolve({ languages: langsQuery }));
      }

      if (skills) {
        const { taxonomyDomain } = extraData;
        queries.push(getSkillsLevels(taxonomyDomain));
      }

      if (roles) {
        const { currentUserLang } = extraData;
        const rolesPayload = {
          query: query,
          ...(isJobRoleSelection ? {} : { kind: 'job_roles' }),
          language: currentUserLang,
          ...extraPayload
        };
        const callJobRole = isJobRoleSelection ? getOmpRoles : getRoles;
        queries.push(callJobRole(rolesPayload));
      }

      if (locations) {
        const locationsPayload = {
          q: query,
          ...extraPayload
        };
        queries.push(getLocationsForPreferences(locationsPayload));
      }

      if (jobFamily) {
        const jobFamilyPayload = {
          query: query,
          ...extraPayload
        };
        queries.push(searchJobFamily(jobFamilyPayload));
      }

      if (organizations) {
        const { currentUserLang, orgType, context } = extraData;
        const orgainzationsPayload = {
          searchText: query,
          orgType,
          context,
          ...extraPayload
        };
        queries.push(getOrganizations(orgainzationsPayload, currentUserLang));
      }

      if (projects && extraPayload && extraPayload?.filters[0]?.filterData[0]) {
        extraPayload.filters[0].filterData[0].filterValue = query;
        queries.push(search(extraPayload));
      }

      if (mentorship && extraPayload && extraPayload?.filters[0]?.filterData[0]) {
        extraPayload.filters[0].filterData[0].filterValue = query;
        queries.push(search(extraPayload));
      }

      if (content && extraPayload && extraPayload.hasOwnProperty('q')) {
        extraPayload.q = query;
        queries.push(fetchCardForTeamAndChannel(extraPayload));
      }

      let resp = await Promise.all(queries);
      if (users && groups && channels) {
        const firstGroupLabel = resp[1].teams[0]?.name?.toLowerCase(); // first item in group search result
        const firstChannelLabel = resp[2].channels[0]?.label.toLowerCase(); // first item in channel search result
        // rearrange resp based on query term
        if (firstGroupLabel?.startsWith(query.toLowerCase())) {
          resp = [resp[1], resp[0], resp[2]];
        }
        if (firstChannelLabel?.startsWith(query.toLowerCase())) {
          resp = [resp[2], resp[1], resp[0]];
        }
      }
      let options = [];
      resp.forEach(promise => {
        // Users
        if (promise.items) {
          promise.items.forEach(u => {
            if (skipCurrentUser && currentUser == u.id) {
              return;
            }
            const usr = extraData.showingOnLandingPage
              ? {
                  value: u.id,
                  label: (
                    <div className="user-list-item">
                      <Avatar
                        user={{
                          imgUrl: u.avatarimages?.tiny,
                          name: u.fullName
                        }}
                        className="avatar-img"
                      />
                      <span>
                        {u.fullName}
                        <div className="smaller-handle">{u.handle}</div>
                      </span>
                    </div>
                  ),
                  id: u.id,
                  textLabel: `${u.fullName} (${u.handle})`,
                  fullName: u.fullName,
                  handle: u.handle,
                  image: u.avatarimages?.tiny
                }
              : {
                  value: u.id,
                  label: (
                    <span>
                      <i className="icon-user-light s-margin-right" role="presentation" />
                      {u.fullName} ({u.handle})
                    </span>
                  ),
                  textLabel: `${u.fullName} (${u.handle})`,
                  externalId: u.externalId,
                  fullName: u.fullName,
                  handle: u.handle,
                  type: 'member'
                };
            if (u.avatarimages) {
              usr['avatarimages'] = u.avatarimages;
            }
            if (u.firstName) {
              usr['firstName'] = u.firstName;
            }
            if (u.lastName) {
              usr['lastName'] = u.lastName;
            }

            options.push(usr);
          });
        }

        // Groups/Teams
        if (promise.teams) {
          promise.teams.forEach(u => {
            options.push({
              value: u.id,
              label: extraData.showingOnLandingPage ? (
                <span>
                  {u.imageUrls?.small ? (
                    <img
                      className="image-thumbnail s-margin-right"
                      src={u.imageUrls.small}
                      alt={u.name}
                    ></img>
                  ) : (
                    <i className="icon-users-fill custom-icon-size" role="presentation" />
                  )}
                  {u.name}
                </span>
              ) : (
                <span>
                  <i className="icon-users s-margin-right" role="presentation" />
                  {u.name}
                </span>
              ),
              textLabel: u.name,
              type: 'group',
              id: u.id,
              title: u.name,
              image: u.imageUrls?.small
            });
          });
        }

        // Channels
        if (promise.channels) {
          promise.channels.forEach(u => {
            options.push({
              value: u.id,
              label: extraData.showingOnLandingPage ? (
                <span aria-label={u.label}>
                  {u.profileImageUrl ? (
                    <img
                      className="image-thumbnail s-margin-right"
                      src={u.profileImageUrl}
                      alt={unescape(u.label)}
                    />
                  ) : (
                    <i className="icon-channel custom-icon-size" role="presentation" />
                  )}
                  {unescape(u.label)}
                </span>
              ) : (
                <span>
                  <i className="icon-channel s-margin-right" role="presentation" />
                  {unescape(u.label)}
                </span>
              ),
              textLabel: unescape(u.label),
              isPrivate: u.isPrivate,
              type: 'channel',
              id: u.id,
              title: unescape(u.label),
              image: extraData.showingOnLandingPage
                ? u.profileImageUrl
                : u.bannerImageUrls?.small_url
            });
          });
        }

        // Topics
        if (promise.topics) {
          const { isParentForTopic = true } = extraData;
          const structuredSuggestion = [];
          const nodes = promise.topics;
          nodes.forEach(topic => {
            const parentLabel =
              isParentForTopic && !!topic?.parent?.label ? topic.parent.label + ' - ' : '';
            structuredSuggestion.push({
              ...topic,
              value: topicValueTranformFn(topic[topicValueField]),
              topic_label: topic.label,
              label: parentLabel ? `${parentLabel} ${topic.label}` : topic.label,
              type: 'topic'
            });
          });

          const suggestions = structuredSuggestion.filter(
            topic => !excludeSkillsIds.includes(topic.id)
          );

          options.push(...suggestions);
        }

        // skills
        if (promise[0]?.data && skills) {
          const structuredSuggestion = [];
          const baseLevel = [];
          // get 1 depth data if 2nd depth is not present
          // save 2nd depth data in base level
          promise[0].data.map(topic => {
            if (topic.data) {
              baseLevel.push(...topic.data);
              // process 2nd depth data if present
              baseLevel.map(childTopic => {
                if (childTopic) {
                  structuredSuggestion.push({
                    topic_id: childTopic.id,
                    topic_label: childTopic.label,
                    path: childTopic.path,
                    topic_name: childTopic.name,
                    label: childTopic.label,
                    value: childTopic.id
                  });
                }
              });
            } else {
              structuredSuggestion.push({
                topic_id: topic.id,
                topic_label: topic.label,
                path: topic.path,
                topic_name: topic.name,
                label: topic.label,
                value: topic.id
              });
            }
          });

          const suggestions = getSuggestions({ structuredSuggestion, tagsSelected: values, query });
          options.push(...suggestions);
        }

        //Roles
        if (promise.roles) {
          const { isSettingsPage, ocg, egt } = extraData;
          let profileRoles = promise.topics;

          if ((isSettingsPage && ocg) || (ocg && !egt)) {
            profileRoles = profileRoles.filter(a => a.name.indexOf('.ocg.') !== -1);
          }

          if ((isSettingsPage && !ocg) || (!ocg && egt)) {
            profileRoles = profileRoles.filter(a => a.name.indexOf('.ocg.') === -1);
          }

          profileRoles.forEach(rol => {
            options.push({ value: rol.id, label: rol.label, type: 'topic' });
          });
        }

        if (promise.time_zones && timezones) {
          promise.time_zones.forEach(timezone => {
            if (timezone.translatedName.toLowerCase().includes(inputValue.toLowerCase())) {
              options.push({
                value: timezone.defaultName,
                label: timezone.translatedName
              });
            }
          });
        }

        if (promise.languages) {
          promise.languages.forEach(lang => {
            if (lang.label.toLowerCase().includes(inputValue.toLowerCase())) {
              options.push({
                id: lang.value,
                label: lang.label,
                value: lang.label
              });
            }
          });
        }

        // Locations
        if (promise.locations) {
          promise.locations.forEach((location = {}) => {
            // hide any possible null values
            const meta = [location.city, location.state, location.country].filter(m => m);
            options.push({
              label: meta.join(', '),
              value: location.id
            });
          });
        }

        // JobFamily
        if (promise.familyItems) {
          promise.familyItems.forEach((family = {}) => {
            options.push({
              label: family.title,
              value: family.id
            });
          });
        }

        //pincodes
        if (promise[0]?.pincode && pincode) {
          if (!promise.length) {
            onClear();
          }
          promise.forEach(topic => {
            options.push({
              ...topic,
              value: topic.pincode,
              label: topic.pincode,
              type: 'pincode'
            });
          });
        }

        //Organizations
        if (promise.divisions) {
          promise.divisions.forEach(org => {
            options.push({
              label: org.title,
              value: org.id
            });
          });
        }

        //Projects
        if (projects && promise.searchResult) {
          promise.searchResult.forEach(project => {
            options.push({
              value: project.id,
              label: (
                <ImageErrorPlaceHolderWithLabel
                  label={project.title}
                  image={project.thumbnail?.url}
                  imageClassNames="image-thumbnail s-margin-right"
                  iconClassNames="project-fallback-icon custom-icon-size"
                  showIcon={true}
                  iconName="icon-image-placeholder"
                  altText={project.title}
                />
              ),
              id: project.id,
              textLabel: project.title,
              title: project.title,
              image: project.thumbnail?.url,
              altText: project.title
            });
          });
        }

        //Mentorships
        if (mentorship && promise.searchResult) {
          promise.searchResult.forEach(mentor => {
            mentor.userId &&
              options.push({
                value: mentor.id,
                label: (
                  <div className="user-list-item">
                    <Avatar
                      user={{
                        imgUrl: mentor.userId.avatarimages?.tiny,
                        name: mentor.userId.fullName
                      }}
                      className="avatar-img"
                    />
                    <span>
                      {mentor.userId.fullName}
                      <div className="smaller-handle">{mentor.userId.handle}</div>
                    </span>
                  </div>
                ),
                id: mentor.id,
                textLabel: mentor.userId?.fullName,
                fullName: mentor.userId?.fullName,
                handle: mentor.userId?.handle,
                image: mentor.userId?.avatarimages?.tiny
              });
          });
        }

        //Content
        if (content && promise.cards) {
          promise.cards.forEach(card => {
            const { cardType } = card;
            const { thumbnail = null, altText = null } =
              extraData.getCardImageDetails?.(card, null, cardType, 'tile', false, true) || {};
            card.title = card.cardTitle;
            options.push({
              value: card.id,
              label: (
                <ImageErrorPlaceHolderWithLabel
                  label={card.cardTitle}
                  image={thumbnail}
                  altText={altText}
                  imageClassNames="image-thumbnail s-margin-right"
                />
              ),
              textLabel: card.cardTitle,
              id: card.id,
              title: card.cardTitle,
              image: thumbnail,
              altText: altText
            });
          });
        }
      });

      if (extraData?.filterResults && typeof extraData.filterResults === 'function') {
        options = options.filter(extraData.filterResults);
      }

      callback(options);
    };

    const handleRequest = useCallback(debounce(_handleRequest, 500), [
      excludeSkillsIds.length > 0 ? excludeSkillsIds.join(',') : '',
      extraData.selectedType
    ]);

    const handleChange = (newValue, actionMeta) => {
      // Check to prevent verified skills from getting deleted
      switch (actionMeta.action) {
        case 'remove-value':
        case 'pop-value':
          if (actionMeta.removedValue.verified) {
            return;
          }
          break;
        default:
          break;
      }
      setValue(clearInputOnSelect ? null : newValue);
      onChange(newValue);
    };

    const handleInputChange = (newInputValue, action) => {
      // only set the input when the action is other than "input-blur", and "menu-close"
      if (action.action !== 'input-blur' && action.action !== 'menu-close') {
        setInputValue(newInputValue);
        if (!newInputValue || newInputValue !== inputValue) {
          onClear();
        }
      }
    };

    // JAWS required reading
    const onFocus = ({ focused, action, context }) => {
      if (action === 'initial-input-focus' || context === 'menu') {
        return '';
      }
      return `${focused?.label?.props?.['aria-label'] || focused?.label} ${translatr(
        'web.common.main',
        'TokenDeletable'
      )}`;
    };

    const LoadingMessage = LoadingMessageProps => {
      return (
        <div
          role="alert"
          className="ed-multi-select__menu-notice--loading"
          {...LoadingMessageProps.innerProps}
          style={LoadingMessageProps.getStyles('loadingMessage', LoadingMessageProps)}
        >
          {translatr('cds.common.main', 'Loading')}
        </div>
      );
    };

    const getNoOptionsMessage = () => {
      if (inputValue?.trim() === '') {
        return null;
      }
      return noOptionsMessage ? noOptionsMessage : translatr('cds.common.main', 'NoResultsFound');
    };

    const NoOptionsMessage = NoOptionsMessageProps => {
      return (
        <div
          role="alert"
          className="ed-multi-select__menu-notice--no-options"
          {...NoOptionsMessageProps.innerProps}
          style={NoOptionsMessageProps.getStyles('noOptionsMessage', NoOptionsMessageProps)}
        >
          {getNoOptionsMessage()}
        </div>
      );
    };

    const ClearIndicator = ({ clearValue }) => {
      return (
        <button
          className="clear-indicator-btn"
          onClick={e => {
            e.preventDefault();
            clearValue();
            if (!ref && inputRef.current) {
              inputRef.current.focus();
            }
          }}
          aria-label={`${translatr('cds.common.main', 'Clear')} ${value?.label}`}
        >
          <span role="presentation" className="close-icon icon-x-mark-Close" />
        </button>
      );
    };
    ClearIndicator.propTypes = {
      clearValue: func
    };

    // This function is used when we dont wanna fetch and display any more results when the maxLimit is passed  and
    // currentSelectedValues from multiselect exceeds the maxLimit.
    // If we do this inside handleRequest then since it is wrapped with deounce flash of loading.. comes on screen which is not expected.
    const loadOptionsWhenMaxLimitExceeded = (query, callback) => {
      callback([]);
    };

    const blockLoadOption = (!!maxLimit && values.length === maxLimit) || disableAddingNew;

    const styles = {
      multiValueRemove: (base, state) => {
        return state.data.verified ? { ...base, display: 'none' } : base;
      }
    };

    const onChangeAriaMessage = val => {
      if (!val) return '';
      if (multiselect) {
        return translatr('web.common.main', 'ContainsXtokens', { numberOf: val?.value?.length });
      }
      return `${val.label} ${translatr('web.common.main', 'Selected')}`;
    };

    useEffect(() => {
      if (multiselect) {
        const input = ref?.current || inputRef?.current;
        input?.inputRef?.setAttribute(
          'aria-roledescription',
          translatr('web.common.main', 'MultiValueComboBox')
        );
      }
    }, []);

    return (
      <>
        <span id="aria-label" style={{ display: 'none' }}>
          {ariaLabel}
        </span>
        <AsyncSelect
          inputId={id}
          required={required}
          className={classNames('ed-multi-select', customAsyncSelectClass)}
          classNamePrefix="ed-multi-select"
          value={value}
          inputValue={inputValue}
          placeholder={placeholder}
          onChange={handleChange}
          onInputChange={handleInputChange}
          isMulti={multiselect}
          components={{
            DropdownIndicator: null,
            LoadingMessage,
            NoOptionsMessage,
            ClearIndicator,
            MultiValueRemove: CustomMultiValueRemove
          }}
          loadOptions={blockLoadOption ? loadOptionsWhenMaxLimitExceeded : handleRequest}
          cacheOptions={blockLoadOption ? false : cacheOptions}
          noOptionsMessage={getNoOptionsMessage}
          isDisabled={disabled}
          isClearable={isClearable}
          aria-labelledby={ariaLabelledby}
          ariaLiveMessages={{
            onFocus,
            onFilter: () => '',
            onChange: onChangeAriaMessage,
            guidance: () => ''
          }}
          onKeyDown={e =>
            pincode && !(e.key === 'Backspace' || NUMBER_REGEX.test(e.key)) && e.preventDefault()
          }
          ref={ref || inputRef}
          onMenuClose={handleMenuClose}
          styles={styles}
        />
      </>
    );
  }
);

SearchInputWrapper.propTypes = {
  placeholder: string,
  multiselect: bool,
  values: oneOfType([array, object]),
  onChange: func,
  users: bool,
  groups: bool,
  channels: bool,
  topics: bool,
  skills: bool,
  pincode: bool,
  organizations: bool,
  projects: bool,
  mentorship: bool,
  content: bool,
  extraData: object,
  extraPayload: object,
  exclude_user_ids: string,
  role: string,
  roles: bool,
  maxLimit: any,
  id: string,
  disabled: bool,
  defaultInputValue: string,
  onClear: func,
  clearInputOnSelect: bool,
  noOptionsMessage: string,
  isClearable: bool,
  topicValueField: string,
  topicValueTranformFn: func,
  locations: bool,
  jobFamily: bool,
  excludeSkillsIds: array,
  cacheOptions: bool,
  skipCurrentUser: bool,
  ariaLabelledby: string,
  defaultAriaLabel: string,
  timezones: bool,
  languages: bool,
  customAsyncSelectClass: string,
  disableAddingNew: bool,
  handleMenuClose: func,
  required: bool
};
