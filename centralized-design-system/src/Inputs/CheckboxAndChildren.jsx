import { useState, useEffect, useCallback, useRef, forwardRef, createRef, useMemo } from 'react';
import { array, func, object, number, bool } from 'prop-types';

import checkChildren from './utils/checkChildren';
import Checkbox from './../Checkbox/index';

const CheckBoxWithArrow = ({ isExpanded, expandSection, items, isChecked, onCheckboxChange }) => {
  return (
    <div className="checkbox-with-arrow">
      {items.children?.length > 0 ? (
        <i
          onClick={expandSection}
          className={`icon-caret-${isExpanded ? 'down' : 'right'}-arrow pointer`}
          role="button"
          tabIndex={-1}
          onKeyDown={e => e.key === 'Enter' && expandSection()}
        />
      ) : (
        <div className="gap" />
      )}
      <Checkbox
        label={items.label}
        name={items.name}
        id={items.id}
        value={items.taxo_id}
        onChange={e => onCheckboxChange(e.target.checked)}
        defaultChecked={false}
        disabled={false}
        checked={isChecked}
        checkboxTabIndex={-1}
        setShowError={null} // setShowError already handled in parent
        ariaLabelHidden={true}
      />
    </div>
  );
};

CheckBoxWithArrow.propTypes = {
  isExpanded: bool.isRequired,
  expandSection: func.isRequired,
  items: object.isRequired,
  isChecked: bool.isRequired,
  onCheckboxChange: func.isRequired
};

const CheckboxAndChildren = forwardRef(
  (
    {
      items,
      isParentChecked,
      setParentIsChecked,
      selection,
      maxSupportedNodes,
      parentLeafNodesMap,
      setShowError,
      tabIndex = -1
    },
    forwardedRef
  ) => {
    const localRef = useRef(null);
    const childRefs = useRef({});
    const currentRef = forwardedRef || localRef;

    const [stateItems, setStateItems] = useState(items);
    const [isChecked, setIsChecked] = useState(items.checked || isParentChecked);
    const [isExpanded, setIsExpanded] = useState(false);

    useEffect(() => {
      setStateItems(items);
      setIsChecked(items.checked || isParentChecked);
    }, [items, isParentChecked]);

    const expandSection = useCallback(() => {
      setIsExpanded(prev => !prev);
    }, [setIsExpanded]);

    const onCheckboxChange = useCallback(
      newChecked => {
        const newItems = { ...stateItems };
        const selectionIds = selection.map(s => s.id);
        const childrenIds = parentLeafNodesMap[newItems.name] || [];
        const currentUnselectedChildren = childrenIds.filter(id => !selectionIds.includes(id));

        if (
          (!selectionIds.includes(newItems.id) && selectionIds.length === maxSupportedNodes) ||
          currentUnselectedChildren.length + selectionIds.length > maxSupportedNodes
        ) {
          setShowError(true);
          return;
        }

        if (newItems.children?.length) {
          newItems.children = checkChildren(newItems.children, newChecked);
        }

        newItems.checked = newChecked;
        setStateItems(newItems);
        setIsChecked(newChecked);
        setParentIsChecked(newChecked, newItems.id, newItems);
        setShowError(false);
      },
      [
        stateItems,
        selection,
        maxSupportedNodes,
        parentLeafNodesMap,
        setParentIsChecked,
        setShowError
      ]
    );

    const decideShouldCheck = useCallback(
      (childChecked, childId, childObj) => {
        const newItems = { ...stateItems };
        const updatedChildren =
          newItems.children?.map(child =>
            child.id === childId ? { ...child, ...childObj } : child
          ) || [];

        newItems.children = updatedChildren;

        const allChecked = updatedChildren.every(child => child.checked);

        newItems.checked = allChecked;
        setIsChecked(allChecked);
        setParentIsChecked(childChecked && allChecked, newItems.id, newItems);
        setStateItems(newItems);
      },
      [stateItems, setParentIsChecked]
    );

    const handleTreeItemKeyDown = useCallback(
      e => {
        const currentItem = currentRef?.current;
        if (!currentItem) return;

        e.preventDefault();
        e.stopPropagation();

        const navigate = dir => {
          let sibling =
            dir === 'next' ? currentItem.nextElementSibling : currentItem.previousElementSibling;
          while (sibling && sibling.getAttribute('role') !== 'treeitem') {
            sibling = dir === 'next' ? sibling.nextElementSibling : sibling.previousElementSibling;
          }
          sibling?.focus();
        };

        switch (e.key) {
          case 'ArrowRight':
            if (!isExpanded) setIsExpanded(true);
            else {
              const group = currentItem.querySelector('[role="group"]');
              const firstChild = group?.querySelector('[role="treeitem"]');
              firstChild?.focus();
            }
            break;
          case 'ArrowLeft':
            if (isExpanded) setIsExpanded(false);
            else {
              const parent = currentItem.closest('ul')?.closest('[role="treeitem"]');
              parent?.focus();
            }
            break;
          case 'ArrowDown':
            navigate('next');
            break;
          case 'ArrowUp':
            navigate('prev');
            break;
          case ' ':
          case 'Enter':
            onCheckboxChange(!isChecked);
            break;
          default:
            break;
        }
      },
      [isExpanded, onCheckboxChange, isChecked, setIsExpanded]
    );

    const children = useMemo(() => {
      if (!isExpanded || !stateItems.children?.length) return null;
      return stateItems.children.map(child => {
        // Ensure stable ref creation
        if (!childRefs.current[child.id]) {
          childRefs.current[child.id] = createRef();
        }
        return (
          <CheckboxAndChildren
            key={child.id}
            ref={childRefs.current[child.id]}
            items={child}
            isParentChecked={isChecked}
            setParentIsChecked={decideShouldCheck}
            selection={selection}
            maxSupportedNodes={maxSupportedNodes}
            parentLeafNodesMap={parentLeafNodesMap}
            setShowError={setShowError}
          />
        );
      });
    }, [
      isExpanded,
      stateItems.children,
      isChecked,
      decideShouldCheck,
      selection,
      maxSupportedNodes,
      parentLeafNodesMap,
      setShowError
    ]);

    return (
      <li
        ref={currentRef}
        key={items.id}
        className="checkbox-section"
        role="treeitem"
        aria-selected={isChecked}
        tabIndex={tabIndex}
        onKeyDown={handleTreeItemKeyDown}
        aria-expanded={isExpanded}
        aria-controls={`checkbox-children-${items.id}`}
      >
        <CheckBoxWithArrow
          isExpanded={isExpanded}
          expandSection={expandSection}
          items={items}
          isChecked={isChecked}
          onCheckboxChange={onCheckboxChange}
        />
        <ul role="group" id={`checkbox-children-${items.id}`}>
          {children}
        </ul>
      </li>
    );
  }
);

CheckboxAndChildren.propTypes = {
  items: object.isRequired,
  isParentChecked: bool,
  setParentIsChecked: func.isRequired,
  selection: array.isRequired,
  maxSupportedNodes: number.isRequired,
  parentLeafNodesMap: object.isRequired,
  setShowError: func.isRequired,
  tabIndex: number
};

export default CheckboxAndChildren;
