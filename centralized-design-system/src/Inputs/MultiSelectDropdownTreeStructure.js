import { useState, useEffect, useRef, useCallback, useId } from 'react';
import { func, object, number, bool, string } from 'prop-types';
import { tr } from 'edc-web-sdk/helpers/translations';
import isEqual from 'lodash/isEqual';

import { translatr } from '../Translatr';
import setClickOutsideFunc from './utils/setClickOutsideFunc';
import getSelectedItems from './utils/getSelectedItems';
import getLeafNodes from './utils/getLeafNodes';
import getParentLeafNodesMap from './utils/getParentLeafNodesMap';
import getSelectedLeafNodes from './utils/getSelectedLeafNodes';
import changeCheckedStatusInTree from './utils/changeCheckedStatusInTree';
import TitleDescription from './TitleDescription';
import CheckboxAndChildren from './CheckboxAndChildren';

const MultiSelectDropdownTreeStructure = ({
  title,
  description,
  items,
  placeholder,
  optional = true,
  disabled = true,
  maxSupportedNodes = 50,
  updateItems,
  updateSelections
}) => {
  const id = useId();
  const uniqueId = `multiselectdropdowntreestructure-title-${id}`;

  const containerRef = useRef(null);
  const selectedItemsContainerRef = useRef(null);
  const selectedTopicUniqueId = `multiselectdropdowntreestructure-selected-topics-${id}`;

  const [open, setOpen] = useState(false);
  const [selection, setSelection] = useState([]);
  const [stateItems, setStateItems] = useState(items || {});
  const [parentLeafNodesMap, setParentLeafNodesMap] = useState({});
  const [value, setValue] = useState('');
  const [showError, setShowError] = useState(false);

  const handleClickOutside = event => {
    if (containerRef && !containerRef.current?.contains(event.target)) {
      setOpen(false);
    }
  };

  setClickOutsideFunc(open, handleClickOutside);

  const handleOnRemove = itemId => {
    const selectionAfterRemove = [...selection].filter(item => item.id !== itemId);
    const itemToBeRemoved = [...selection].find(item => item.id === itemId);
    const changedCheckedStatusInTreeObj = changeCheckedStatusInTree(stateItems, itemToBeRemoved);
    setSelection([...selectionAfterRemove]);
    updateSelections([...selectionAfterRemove]);
    setOpen(false);
    setStateItems(changedCheckedStatusInTreeObj);
    updateItems(changedCheckedStatusInTreeObj);
  };

  const handleArrowNavigationTags = useCallback((event, direction, currentIndex, listItems) => {
    event.preventDefault();
    const defaultIndex = 0;

    const targetIndex =
      currentIndex !== -1
        ? (currentIndex + direction + listItems.length) % listItems.length
        : defaultIndex;

    listItems[targetIndex]?.focus();
  }, []);

  const handleOnKeyDownTag = useCallback(
    e => {
      const listItems = Array.from(
        containerRef.current?.querySelectorAll('.ed-tag-container') || []
      );
      const current = document.activeElement;
      const currentIndex = listItems.indexOf(current);

      const removeValue = () => {
        if (currentIndex >= 0 && currentIndex < selection.length) {
          e.preventDefault();
          handleOnRemove(selection[currentIndex]?.id);
          if (currentIndex > 0) {
            listItems[currentIndex - 1]?.focus();
          } else if (listItems.length > 1) {
            listItems[0]?.focus();
          }
        }
      };

      const KEY_ACTIONS = {
        Backspace: removeValue,
        Delete: removeValue,
        ArrowDown: () => handleArrowNavigationTags(e, 1, currentIndex, listItems),
        ArrowRight: () => handleArrowNavigationTags(e, 1, currentIndex, listItems),
        ArrowUp: () => handleArrowNavigationTags(e, -1, currentIndex, listItems),
        ArrowLeft: () => handleArrowNavigationTags(e, -1, currentIndex, listItems)
      };

      const action = KEY_ACTIONS[e.key];
      if (action) action();
    },
    [containerRef, selection, handleOnRemove, handleArrowNavigationTags]
  );

  const toggle = () => {
    setOpen(!open);
  };

  const decideShouldCheck = (isChildChecked, childId, childObj) => {
    const newItems = { ...stateItems };

    // Means root item is checked
    if (newItems.id === childObj.id) {
      newItems.checked = childObj.checked;
      newItems.children = childObj.children;
    } else {
      if (newItems.children?.length) {
        newItems.children = newItems.children.map(child => {
          if (child.id === childId) {
            child.checked = childObj.checked;
            if (child.children?.length) {
              child.children = childObj.children;
            }
          }
          return child;
        });
      }
    }

    if (isChildChecked === false) {
      newItems.checked = false;
    } else {
      const allChecked = newItems.children?.every(child => child.checked);
      if (allChecked) {
        newItems.checked = true;
      }
    }
    const newLeafNodes = getLeafNodes(newItems);
    const selectionsArray = getSelectedLeafNodes(newLeafNodes);
    updateSelections(selectionsArray);
    setSelection(selectionsArray);
    setStateItems(newItems);
    updateItems(newItems);
  };

  const getSelectedTopicsMsg = () => {
    return tr('%{selectionLen}/%{maxSupportedNodes} topics selected', {
      selectionLen: selection.length,
      maxSupportedNodes
    });
  };

  const getErrorMsg = () => {
    return (
      <div
        className={`supporting-text topics-msg max-topics-error error-text`}
        aria-live="assertive"
        role="alert"
      >
        {translatr('cds.common.main', 'MaximumMaxsupportednodesTopicsCanBeSelected', {
          maxSupportedNodes
        })}
      </div>
    );
  };

  useEffect(() => {
    setValue(
      getSelectedItems(
        selection,
        placeholder,
        handleOnRemove,
        false,
        25,
        selectedItemsContainerRef,
        handleOnKeyDownTag
      )
    );
  }, [selection]);

  useEffect(() => {
    if (disabled || isEqual(stateItems, items)) {
      return;
    }
    if (Object.keys(stateItems).length === 0) {
      setStateItems({ ...items });
      const leafNodes = getLeafNodes(items);
      const selectionsArray = getSelectedLeafNodes(leafNodes);
      setParentLeafNodesMap(getParentLeafNodesMap(leafNodes));
      updateSelections(selectionsArray);
      setSelection(getSelectedLeafNodes(leafNodes));
    }
  }, [disabled, items]);

  return (
    <div className="ed-input-container">
      <div className="input-group" ref={containerRef}>
        <TitleDescription
          title={title}
          optional={optional}
          description={description}
          controlId={uniqueId}
        />
        <div
          className={`ed-dropdown tree-structure
          ${selection.length ? 'dd-multiValue' : 'dd-singleValue'} 
          ${disabled ? 'disabled' : ''}
          ${open ? 'ed-dropdown-active' : ''}`}
          onClick={() => !disabled && toggle()}
          onKeyDown={event => {
            if (!disabled && ['Enter', 'ArrowDown', ' '].includes(event.key)) {
              event.preventDefault();
              if (
                (event.key === 'ArrowDown' && !open) ||
                event.key === 'Enter' ||
                event.key === ' '
              ) {
                toggle();
              }
              return;
            }
            if (['ArrowLeft', 'ArrowRight'].includes(event.key)) {
              event.preventDefault();
              handleOnKeyDownTag(event);
            }
          }}
          role="listbox"
          aria-expanded={open}
          aria-controls="multiselectdropdowntreestructure-dropdown-list"
          aria-multiselectable="true"
          tabIndex="0"
          aria-labelledby={`${uniqueId}-label`}
          aria-describedby={selectedTopicUniqueId}
          id={uniqueId}
        >
          {value}
        </div>
        {open && (
          <div className="dropdown-container padding-bottom block">
            {showError && getErrorMsg()}
            <div
              className={`supporting-text topics-msg topics-selected-indicator ${
                selection.length >= maxSupportedNodes ? 'error-text' : ''
              }`}
              aria-live="polite"
              role="status"
            >
              {getSelectedTopicsMsg()}
            </div>
            <ul
              className="dropdown-scrollable-container"
              id="multiselectdropdowntreestructure-dropdown-list"
              role="tree"
              aria-orientation="vertical"
            >
              <CheckboxAndChildren
                items={stateItems}
                isParentChecked={items.checked}
                setParentIsChecked={decideShouldCheck}
                selection={selection}
                maxSupportedNodes={maxSupportedNodes}
                parentLeafNodesMap={parentLeafNodesMap}
                setShowError={setShowError}
                tabIndex={0}
              />
            </ul>
          </div>
        )}
        <div className="text-right supporting-text" id={selectedTopicUniqueId}>
          {getSelectedTopicsMsg()}
        </div>
      </div>
    </div>
  );
};

MultiSelectDropdownTreeStructure.propTypes = {
  title: string,
  items: object,
  updateItems: func,
  description: string,
  optional: bool,
  disabled: bool,
  maxSupportedNodes: number,
  placeholder: string,
  updateSelections: func
};

export default MultiSelectDropdownTreeStructure;
