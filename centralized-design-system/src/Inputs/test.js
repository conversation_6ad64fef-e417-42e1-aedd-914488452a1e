import React, { useState, useRef, useEffect } from 'react';
import moment from 'moment';

import {
  AreaInput,
  InputDropDown,
  LinearProgressBar,
  Select,
  SelectMonth,
  SelectYear,
  SearchInput,
  TimePicker,
  InputWithButton,
  MultiSelectSearch
} from './index';
import { GroupedSelect } from './GroupedSelect';
import TextField from './TextField';
import RangeSlider from './RangeSlider';
import MultiSelectDropdownTreeStructure from './MultiSelectDropdownTreeStructure';
import MultiSelectDropdownWithCheckbox from './MultiSelectDropdownWithCheckbox';
import treeStructureDummyData from './utils/treeStructureDummyData';
import multiSelectCheckboxDummyData from './utils/multiSelectCheckboxDummyData';
import TitleDescription from './TitleDescription';

const Test = () => {
  const [number, setNumber] = useState('');
  const [numberErrorField, setNumberErrorField] = useState('');
  const [password, setPassword] = useState('');
  const [value, setValue] = useState('');
  const [text, setText] = useState('');
  const [text1, setText1] = useState('');
  const [seletedItems1, setItems1] = useState(null);
  const [seletedItems2, setItems2] = useState([]);
  const [email, setEmail] = useState('');
  const [searchStr, setSearchStr] = useState('');
  const [isRange, setRange] = useState(0);
  const [treeStructure, setTreeStructure] = useState({});
  const [testData, setTestData] = useState([]);
  const [selections, setSelections] = useState([]);
  const [selectedData, setSelectedData] = useState([]);

  const inputEl = useRef(null);
  const rangeSlider = useRef();

  const now = moment();

  useEffect(() => {
    setTreeStructure(treeStructureDummyData);
    setTestData(multiSelectCheckboxDummyData);
  }, []);

  const items = [
    {
      id: 1,
      value: 'item1',
      label: 'item1'
    },
    {
      id: 2,
      value: 'item2',
      label: 'item2'
    },
    {
      id: 3,
      value: 'item3',
      label: 'item3'
    },
    {
      id: 4,
      value: 'The Future of Work is Here. Welcome to EdCast AI-powered Knowledge Cloud',
      label: 'The Future of Work is Here. Welcome to EdCast AI-powered Knowledge Cloud'
    }
  ];
  const [defaultSelection1, setDefaultSelection1] = useState([items[1]]);
  const cb = value1 => {
    console.log(moment(value1, 'HH:mm:ss'));
  };

  const updateSelectedItem = item => {
    seletedItems1; //to eliminate the eslint errors, as variable is not used anywhere so just added here since it is dummy component
    setItems1(item);
    setDefaultSelection1(null);
  };

  const handleSearch = str => {
    setSearchStr(str);
  };

  const inputChangeHandler = value1 => {
    console.log('Text:', value1);
  };

  const handleSubmit = () => {
    inputEl.current.value = '';
  };

  const sliderHandler = () => {
    changeSliderBar();
  };

  const changeSliderBar = () => {
    isRange; //to eliminate the eslint errors, as variable is not used anywhere so just added here since it is dummy component
    rangeSlider.current.style.setProperty('--seek-before-width', rangeSlider.current.value + '%');
    setRange(rangeSlider.current.value);
  };

  return (
    <React.Fragment>
      <h5>Inputs</h5>
      <br />
      <div className="test-group">
        <div className="test-component">
          <TextField
            title="Book Title"
            description="This title will be publicly visible, make sure it is wasy to read and memorable."
            placeholder="Placeholder Text here"
            setValue={setValue}
            defaultValue={value}
          />
          <br />
          <TextField
            type="email"
            title="Email"
            description="This email will be used to send all types of scam attempts."
            placeholder="e.g.: <EMAIL>"
            setValue={setEmail}
            defaultValue={email}
          />
          <br />
          <TextField
            required
            type="password"
            title="Password"
            setValue={setPassword}
            defaultValue={password}
          />
          <br />
          <TextField
            optional
            type="number"
            title="Number of Learners"
            placeholder="enter number of learners here"
            setValue={setNumber}
            defaultValue={number} //to eliminate the eslint errors, as variable is not used anywhere so just added here since it is dummy component
          />
          <TextField
            type="number"
            title="Error in field, type number to fix"
            placeholder="type here"
            error={'error'}
            setValue={setNumberErrorField}
            defaultValue={numberErrorField} //to eliminate the eslint errors, as variable is not used anywhere so just added here since it is dummy component
          />
          <TextField
            type="email"
            title="Disabled email field"
            placeholder="type here"
            disabled
            setValue={() => {}}
            defaultValue="<EMAIL>"
          />
          <br />
          <div className="search-container">
            <SearchInput
              value={searchStr}
              placeholder="Search By User Name"
              onSearch={handleSearch}
            />
          </div>

          <br />
          <br />
          <AreaInput
            setValue={setText}
            value={text}
            title="Text Area Title"
            description="Lorem ipsum dolor sit amet, consectetur adipiscing elit."
            placeholder="Your input starts here."
          />
          <br />
          <AreaInput
            required
            setValue={setText1}
            value={text1}
            title="Required Text Area Title"
            description="Lorem ipsum dolor sit amet, consectetur adipiscing elit. (With defaultTextInput)"
            defaultTextInput="Your defaultTextInput is here, you can edit directly."
          />
          <br />
          <br />
        </div>
        <div className="test-code">
          <pre>
            {`
const [number, setNumber] = useState('');
const [password, setPassword] = useState('');
const [value, setValue] = useState('');
const [text, setText] = useState('');
const [text1, setText1] = useState('');
const [searchStr, setSearchStr] = useState('');

const handleSearch = str => {
  setSearchStr(str);
}

<TextField
    title='Book Title'
    decscription='This title will be publicly visible, make sure it is wasy to read and memorable.'
    placeholder='Placeholder Text here'
    setValue={setValue}
/>
<TextField
    type='email'
    title='Email'
    decscription='This email will be used to send all types of scam attempts.'
    placeholder='e.g.: <EMAIL>'
    setValue={setEmail}
/>
<TextField
    type='password'
    title='Password'
    setValue={setPassword}
/>
<TextField
    type='number'
    title='Number of Learners'
    placeholder='enter number of learners here'
    setValue={setNumber}
/>
<SearchInput
  value={searchStr}
  placeholder="Search By User Name"
  onSearch={handleSearch}
/>
<AreaInput
    setValue={setText}
    title="Text Area Title"
    decscription="Lorem ipsum dolor sit amet, consectetur adipiscing elit."
    placeholder="Your input starts here."
/>
<AreaInput
  setValue={setText1}
  title="Text Area Title"
  decscription="Lorem ipsum dolor sit amet, consectetur adipiscing elit."
  defaultTextInput="Your defaultTextInput is here, you can edit directly."
/>
`}
          </pre>
        </div>
        <div className="test-component">
          <Select title="Simple select" items={items} />
          <Select title="Simple select required" items={items} required />
          <Select title="Simple select optional" items={items} optional="optional" />
          <Select title="Simple select disabled" disabled items={items} />
        </div>
        <div className="test-code">
          <pre>
            {`const items = [
    {
        id: 1,
        value: 'item1'
    },
    {
        id: 2,
        value: 'item2'
    },
    {
        id: 3,
        value: 'item3'
    },
    {
        id: 4,
        value: 'The Future of Work is Here. Welcome to EdCast AI-powered Knowledge Cloud'
    }
];

function onChange(item) {
  // Returns item object
  {
    id: 1,
    value: 'item1'
  }
}

<Select title="Simple select" items={items} onChange={onChange} />

`}
          </pre>
        </div>
        <div className="test-component">
          <GroupedSelect
            title="Simple grouped select"
            description="Select the level at which you would like to enhance your skill."
            onChange={opt => console.dir({ opt })}
            required
            items={[
              {
                label: 'Recommended',
                options: [
                  { value: '1', label: 'JavaScript | beginner' },
                  { value: '2', label: 'Java | master' },
                  { value: '3', label: 'JVC | advance' }
                ]
              },
              {
                label: 'All',
                options: [
                  { value: '4', label: 'Leadership' },
                  { value: '5', label: 'Listening', disabled: true },
                  { value: '6', label: 'Storytelling' }
                ]
              }
            ]}
          />
        </div>
        <div className="test-code">
          <pre>
            {`const items = [
              {
                label: 'Recommended',
                options: [
                  { value: '1', label: 'JavaScript | beginner' },
                  { value: '2', label: 'Java | master' },
                  { value: '3', label: 'JVC | advance' }
                ]
              },
              {
                label: 'All',
                options: [
                  { value: '4', label: 'Leadership' },
                  { value: '5', label: 'Listening' },
                  { value: '6', label: 'Storytelling' }
                ]
              }
            ];

<GroupedSelect title="Simple grouped select" items={items} description={description} onChange={onChange} />

`}
          </pre>
        </div>
        <div className="test-component">
          <SelectYear title="Simple year select" startYear={2021} endYear={2034} />
        </div>
        <div className="test-code">
          <pre>
            {`
            function onChange(item) {
            // Returns item object
            {
             id: 2021,
             value: '2021'
            }
          }

<SelectYear title="Simple Year select" onChange={onChange} startYear={2021} endYear={2031} />`}
          </pre>
        </div>
        <div className="test-component">
          <SelectMonth title="Simple month select" startMonth={1} endMonth={12} />
        </div>
        <div className="test-code">
          <pre>
            {`
            function onChange(item) {
            // Returns item object
            {
             id: 1,
             value: 'January'
            }
          }

<SelectMonth title="Simple Month select" onChange={onChange} startMonth={1} endMonth={12} /> `}
          </pre>
        </div>
        <div
          className="test-component"
          style={{ display: 'flex', justifyContent: 'space-between' }}
        >
          <InputDropDown
            title={'Single Select Drop Down Title'}
            decscription={'Supporting Text'}
            items={items}
            setSelected={updateSelectedItem}
            defaultSelection={defaultSelection1}
            preLabel={'Assigned By:'}
          />
          <InputDropDown
            title={'Disabled InputDropDown'}
            decscription={'Supporting Text'}
            disabled
            items={items}
            setSelected={updateSelectedItem}
            defaultSelection={defaultSelection1}
            preLabel={'Assigned By:'}
          />
          <InputDropDown
            title={'Multi Select Drop Down title'}
            items={items}
            setSelected={setItems2}
            multiSelect={true}
            placeholder={'Choose option'}
            value={seletedItems2} //to eliminate the eslint errors, as variable is not used anywhere so just added here since it is dummy component
          />
        </div>
        <div className="test-code">
          <pre>
            {`
    const [seletedItems1, setItems1] = useState(null);
    const [seletedItems2, setItems2] = useState([]);
    const items = [
        {
            id: 1,
            value: 'item1'
        },
        {
            id: 2,
            value: 'item2'
        },
        {
            id: 3,
            value: 'item3'
        },
        {
            id: 4,
            value: 'The Future of Work is Here. Welcome to EdCast AI-powered Knowledge Cloud'
        }
    ];
    const [defaultSelection1, setDefaultSelection1] = useState([items[1]]);
    const updateSelectedItem = item => {
        setItems1(item);
        setDefaultSelection1(null);
    }

    <InputDropDown
        title="Single Select Drop Down Title"
        decscription="Supporting Text"
        items={items}
        setSelected={updateSelectedItem}
        defaultSelection={defaultSelection1}
        preLabel={'Assigned By:'}
    />
    <InputDropDown
        title="Multi Select Drop Down title"
        items={items}
        setSelected={setItems2}
        multiSelect={true}
        placeholder={'Choose option'}
    />
`}
          </pre>
        </div>
        <div
          className="test-component"
          style={{ display: 'flex', justifyContent: 'space-between' }}
        >
          <TimePicker time={now} cb={cb} />
          <TimePicker time={now} cb={cb} use12Hours={true} />
        </div>
        <div className="test-code">
          <pre>
            {`
const now = moment();
const cb = value => {
    console.log(moment(value, "HH:mm:ss"));
}
<TimePicker time={now} cb={cb}/>
<TimePicker time={now} cb={cb} use12Hours={true}/>
                        `}
          </pre>
        </div>
        <div className="test-component">
          <LinearProgressBar value={0} />
          <LinearProgressBar value={50} />
          <LinearProgressBar value={100} />
          <LinearProgressBar value={10} hideLabel={true} />
        </div>
        <div className="test-code">
          <pre>
            {`
<LinearProgressBar value={0}/>
<LinearProgressBar value={50}/>
<LinearProgressBar value={100}/>
<LinearProgressBar value={30} hideLabel={true}/>
                        `}
          </pre>
        </div>
        <div className="test-component">
          <InputWithButton
            id="input-with-btn-test-id-1"
            aria-label={'Input with attached button'}
            placeholder={'Type Something...'}
            onChangeHandler={inputChangeHandler}
            onSubmitHandler={handleSubmit}
            isDisabled={false}
            buttonContent={'Send'}
            ref={inputEl}
            label="Input Field With Attached Button"
            decscription="Lorem ipsum dolor sit amet, consectetur adipiscing elit."
          />
          <br />
          <InputWithButton
            onSubmitHandler={() => {
              /* logic */
            }}
            buttonContent={
              <>
                <span className="icon-plus-circle" />
                <span className="ml-5">Add</span>
              </>
            }
            label="Input Field With Attached Button (Icon + Text)"
          />
        </div>
        <div className="test-code">
          <pre>
            {`
              const inputChangeHandler = (value) => {
                console.log("Text:", value);
              }
            
              const handleSubmit = () => {
                inputEl?.current?.value = "";
              }

              <InputWithButton
                id="input-with-btn-test-id"
                aria-label={"Input with attached button"}
                placeholder={"Type Something..."}
                onChangeHandler={inputChangeHandler}
                onSubmitHandler={handleSubmit}
                isDisabled={false}
                buttonContent={'Send'}
                ref={inputEl}
                label="Input Field With Attached Button"
                decscription="Lorem ipsum dolor sit amet, consectetur adipiscing elit."
              />
              <br></br>
              <InputWithButton
                onSubmitHandler={() => {/* logic */}}
                buttonContent={<><span className="icon-plus-circle"></span><span className="ml-5">Add</span></>}
                label="Input Field With Attached Button (Icon + Text)"
              />
          `}
          </pre>
        </div>
      </div>
      <h5>Input Range Slider</h5>
      <div className="test-group">
        <div className="test-component">
          <RangeSlider
            ref={rangeSlider}
            handleSlider={sliderHandler}
            className="range-slider-bar"
          />
        </div>
        <div className="test-code">
          <pre>
            {` 
              const [isRange, setRange] = useState(30);

              const rangeSlider = useRef();

              cconst sliderHandler = () => {
                changeSliderBar();
              }

              const changeSliderBar = () => {
                rangeSlider.current.style.setProperty(
                  '--seek-before-width',
                  rangeSlider.current.value +'%'
                );
                setRange(rangeSlider.current.value);
              };

              <RangeSlider ref={rangeSlider} handleSlider={sliderHandler} className='range-slider-bar'/>

              `}
          </pre>
        </div>
      </div>

      <h5>MultiSelect Search</h5>
      <div className="test-group">
        <div className="test-component">
          <MultiSelectSearch
            placeholder="Search"
            value={seletedItems2}
            onChange={setItems2}
            options={items.map(item => ({ ...item, label: item.value }))}
          />
        </div>
        <div className="test-code">
          <pre>
            {`
                const [selections, setSelections] = useState([]);
                const options = [{ id: 1, label: 1 }, { id: 2, label: 2}];

                <MultiSelectSearch
                  placeholder="Search"
                  value={selections}
                  onChange={setSelections}
                  options={options}
                />
              `}
          </pre>
        </div>
      </div>

      <h5>MultiSelect Dropdown For Tree Structure</h5>
      <div className="test-group">
        <div className="test-component">
          <MultiSelectDropdownTreeStructure
            title="Items"
            description="Test Description for items"
            placeholder="Click here to select topics"
            items={treeStructure}
            disabled={Object.keys(treeStructure).length === 0}
            maxSupportedNodes={50}
            updateItems={setTreeStructure}
            updateSelections={setSelections}
            value={selections}
          />
        </div>
        <div className="test-code">
          <pre>
            {`
                const [treeStructure, setTreeStructure] = useState({});
                const [selections, setSelections] = useState([]);
                useEffect(() => {
                  setTreeStructure(data); // Data fetched from API
                },[]);

                <MultiSelectDropdownTreeStructure
                  title="Items"
                  description="Test Description for items"
                  placeholder="Click here to select items"
                  items={treeStructure}
                  disabled={Object.keys(treeStructure).length === 0}
                  maxSupportedNodes={50}
                  updateItems={setTreeStructure}
                  updateSelections={setSelections}
                />
              `}
          </pre>
        </div>
      </div>

      <h5>MultiSelect Dropdown With Checkboxes and Select/Unselect All</h5>
      <div className="test-group">
        <div className="test-component">
          <MultiSelectDropdownWithCheckbox
            title="City(ies)"
            description="Test Description."
            placeholder="Click here to select items"
            items={testData}
            disabled={testData.length === 0}
            updateItems={setTestData}
            updateSelections={setSelectedData}
            value={selectedData}
          />
        </div>
        <div className="test-code">
          <pre>
            {`
                const [testData, setTestData] = useState([]);
                const [selectedData, setSelectedData] = useState([]);
                useEffect(() => {
                  setTestData(data); // Data fetched from API
                },[]);

                <MultiSelectDropdownWithCheckbox
                  title="City(ies)"
                  description="Test Description."
                  placeholder="Click here to select items"
                  items={testData}
                  disabled={testData.length === 0}
                  updateItems={setTestData}
                  updateSelections={setSelectedData}
                />
              `}
          </pre>
        </div>
      </div>

      <h5>Title and Description</h5>
      <div className="test-group">
        <div className="test-component">
          <TitleDescription
            title="Title"
            description="this is a description"
            optional={false}
            required={true}
          />
        </div>
        <div className="test-code">
          <pre>
            {` 
              <TitleDescription
                title="Title"
                description="this is a description"
                optional={false}
                required={true}
              />
            `}
          </pre>
        </div>
      </div>
    </React.Fragment>
  );
};

export default Test;
