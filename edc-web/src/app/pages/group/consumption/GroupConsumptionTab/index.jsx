import React, { useContext, useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { Route, useNavigate, useParams, useSearchParams } from 'react-router-dom';
import TabBar from 'centralized-design-system/src/TabBar';
import { GroupConsumptionContext } from '../GroupConsumptionProvider';
import GroupConsumptionContent from './../GroupConsumptionContent';
import GroupAssignments from './../GroupConsumptionContent/GroupAssignments';
import GroupAssociatedChannel from './../GroupConsumptionContent/GroupAssociatedChannel';
import GroupCustomTabContent from './../GroupConsumptionContent/GroupCustomTabContent';
import AllFeaturedCardListing from './../GroupConsumptionContent/AllFeaturedCardListing';
import Skills from './../GroupConsumptionContent/Skills';
import About from './../About';
import Spinner from '@components/common/spinner';
import NoContentAvailable from '@components/common/NoContentAvailable';
import { translatr } from 'centralized-design-system/src/Translatr';
import { RootRoutes } from '../../../../Router';
import GroupPostingFeed from '@pages/group/consumption/GroupPosting/GroupPostingFeed/GroupPostingFeed';
import CohortTab from '../../cohorts';

const GroupConsumptionTab = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { slug, '*': tabId } = useParams();
  const [isCurrentTabExist, setIsCurrentTabExist] = useState(true);

  const {
    groupTabs,
    isContentIsReloading,
    editPageHistory,
    setEditPageHistory,
    isGroupEditable,
    groupDetails
  } = useContext(GroupConsumptionContext);
  const isDefaultRouteSet = useRef(false);
  const prefix = `/teams/${slug}`;
  const searchQuery = groupDetails?.enablePostingFeed && searchParams ? `?${searchParams}` : '';

  useEffect(() => {
    if (!isDefaultRouteSet.current && !window.location.pathname.split('/')[3]) {
      const defaultRoute = getDefaultRoute();
      isDefaultRouteSet.current = true;
      const route = !!defaultRoute ? `/${defaultRoute}` : '';
      navigate(prefix + route + searchQuery, { replace: true });
    }

    const doesCurrentTabExists = !!groupTabs.find(tab => tab.link === window.location.pathname);
    setIsCurrentTabExist(doesCurrentTabExists);
  }, []);

  const getDefaultRoute = () => {
    if (groupTabs.length && groupTabs[0]?.link && groupTabs[0]?.name !== 'Home') {
      const arr = groupTabs[0]?.link.split('/');
      return arr[arr.length - 1];
    }
    return ``;
  };

  if (groupTabs.length === 0 && isDefaultRouteSet.current && window.location.pathname !== prefix) {
    navigate(prefix + searchQuery, { replace: true });
  }

  return (
    <>
      {isContentIsReloading ? (
        <Spinner />
      ) : (
        <>
          {groupTabs.length > 0 && (
            <TabBar
              tabs={[...groupTabs]}
              className="mb-16"
              setPageHistory={setEditPageHistory}
              pageHistory={editPageHistory}
            />
          )}
          {groupTabs.length <= 0 || !isCurrentTabExist ? (
            <NoContentAvailable
              message={translatr('web.group.main', 'GroupSectionDeletedOrDisabled')}
              redirectPath={`${prefix}/edit/content-layout`}
              isEditable={isGroupEditable}
              type={translatr('web.common.main', 'group')}
              editModalButtonLabel={translatr(
                'web.group.main',
                'NoContentAvailableEditButtonLabelGroup'
              )}
              description={translatr(
                'web.common.main',
                'PleaseEnableSectionsToDisplayContentForThisGroup'
              )}
            />
          ) : (
            <RootRoutes>
              <Route
                path={prefix}
                element={
                  groupDetails?.enablePostingFeed ? (
                    <GroupPostingFeed />
                  ) : (
                    <GroupConsumptionContent />
                  )
                }
              />
              <Route path={`${prefix}/assignments`} element={<GroupAssignments />} />
              <Route path={`${prefix}/channels`} element={<GroupAssociatedChannel />} />
              <Route path={`${prefix}/about`} element={<About />} />
              <Route path={`${prefix}/featured`} element={<AllFeaturedCardListing />} />
              <Route path={`${prefix}/skills`} element={<Skills />} />
              <Route path={`${prefix}/cohorts`} element={<CohortTab />} />
              <Route path={`${prefix}/:tabName`} element={<GroupCustomTabContent key={tabId} />} />
            </RootRoutes>
          )}
        </>
      )}
    </>
  );
};

GroupConsumptionTab.propTypes = {
  data: PropTypes.object
};

export default GroupConsumptionTab;
