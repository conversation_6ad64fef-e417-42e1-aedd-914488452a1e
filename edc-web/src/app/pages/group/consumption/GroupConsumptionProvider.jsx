import React, { createContext, useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { node, object, string, func, bool, array, number } from 'prop-types';
import {
  getNewTranslatedLabel,
  getProfileLanguage
} from '../../../components/common/TranslatedLabel';
import {
  getUsersInAGroupV2,
  fetchPinnedcards,
  pinCard,
  unpinCard
} from 'edc-web-sdk/requests/groups.v2';
import { fetchGroupDetails, isGroupEditableFunc } from './utils';
import { getSpecificUserInfo } from '../../../actions/currentUserActions';
import getTabPath from '@utils/getTabPath';
import { getAdpUserStatus } from 'edc-web-sdk/requests/adp';
import LD from '../../../containers/LDStore';
import { open_v2 as openSnackBar } from '../../../actions/snackBarActions';
import { translatr } from 'centralized-design-system/src/Translatr';
import { getUserScores } from 'edc-web-sdk/requests/teams';
import { Permissions } from '../../../utils/checkPermissions';
import { SPECIAL_CHAR_REGEX } from '../../../constants/regexConstants';
import sortBy from 'lodash/sortBy';
import { declineInviteToGroup } from 'actions/groupsActionsV2';
import { getLMSAvailability } from '@utils/fetchAccessibleCards/helpers';
import isLmsProviderEnabledInstance from '@utils/isLmsProviderEnabledInstance';
import { ThemeId } from 'centralized-design-system/src/Theme/ThemeInterface';
import { isPostingFeedEnabled } from '@pages/group/consumption/GroupPosting/utils';
import { getHtmlWidget } from 'edc-web-sdk/requests/htmlWidget';
import { isCohortEnabled } from '@pages/group/cohorts/utils';

export const GroupConsumptionContext = createContext();

const GroupConsumptionProvider = ({
  children,
  isCurrentUserIsAdmin,
  isCurrentUserSuperAdmin,
  updateUserInfo,
  isCurrentUserId,
  dispatch,
  hideMembersGroupList,
  leaderboard,
  currentUserEmail,
  orgId,
  isTeamActivityVisible,
  allLangs,
  currentUserLang,
  currentAppLang,
  assignmentLabel,
  theme
}) => {
  const navigate = useNavigate();
  const { slug } = useParams();

  const [groupDetails, setGroupDetails] = useState(null);
  const [groupTabs, setGroupTabs] = useState([]);
  const [isFeaturedContentIsLoading, setIsFeaturedContentIsLoading] = useState(true);
  const [featuredCards, setFeaturedCards] = useState([]);
  const [featuredCardIds, setFeaturedCardIds] = useState([]);
  const [totalFeaturedCards, setTotalFeaturedCards] = useState(0);
  const [pendingUsersCount, setPendingUsersCount] = useState(0);
  const [isGroupEditable, setIsGroupEditable] = useState(false);
  const [isAdpUser, setIsAdpUser] = useState(false);
  const [isContentIsReloading, setIsContentIsReloading] = useState(false);
  const [leaderBoardUsers, setLeaderBoardUsers] = useState([]);
  const [currentUserLeaderBoardStat, setCurrentUserLeaderBoardStat] = useState(null);
  const [totalLeaderBoardUsers, setTotalLeaderBoardUsers] = useState(0);
  const [editPageHistory, setEditPageHistory] = useState([]);
  const [isDeclineInvitationModalOpen, setIsDeclineInvitationModalOpen] = useState(false);
  const [invitationGroupId, setInvitationGroupId] = useState('');
  const [invitationGroupName, setInvitationGroupName] = useState('');
  const [isLeaderboardLoading, setIsLeaderBoardLoading] = useState(false);
  const [filterByLanguage, setFilterByLanguage] = useState(true);
  const prefix = `/teams/${slug}`;

  const enableLeaderboard =
    Permissions['enabled'] !== undefined && Permissions.has('GET_LEADERBOARD_INFORMATION');
  const showLeaderBoardToMember =
    (groupDetails?.isPrivate || groupDetails?.userBelongsToGroup) && enableLeaderboard;

  const profileLang = getProfileLanguage({
    langs: allLangs,
    currentUserLang: currentUserLang || currentAppLang || 'en'
  });
  const label = getNewTranslatedLabel({
    labelObj: assignmentLabel,
    appName: 'web.group.main',
    profileLanguage: profileLang,
    newDefaultLabel: 'My Assignments'
  });

  const getTabName = tabName => {
    switch (tabName) {
      case 'Featured':
        return translatr('web.group.main', 'featuredPosts');
      case 'Assigned':
        return label;
      case 'Channels':
        return translatr('web.group.main', 'associatedChannels');
      case 'About':
        return translatr('web.group.main', 'About');
      case 'Home':
        return translatr('web.group.main', 'Home');
      default:
        return tabName;
    }
  };

  // load initial data for group
  const loadInitialData = async () => {
    if (slug && !groupDetails) {
      const groupData = await fetchGroupDetails(slug, dispatch);
      const newTabs = [];
      setIsGroupEditable(
        isGroupEditableFunc(groupData, isCurrentUserIsAdmin, isCurrentUserSuperAdmin)
      );
      const sortedArray = sortBy(groupData?.carousels, o => o.index);
      sortedArray.forEach(tab => {
        if (tab.visible) {
          newTabs.push({
            name: tab.default_label,
            link: getLink(tab.default_label, tab.id),
            type: tab.type,
            carouselId: tab.id,
            index: tab.index,
            label: getTabName(tab.default_label)
          });
        }
      });

      if (isCohortEnabled()) {
        //TODO: add on backend level??
        newTabs.push({
          name: 'Cohort',
          link: getLink('cohorts', 'cohorts'),
          label: 'Cohorts',
          index: newTabs.length
        });
      }

      setGroupTabs(newTabs);

      const postingFeedEnabled = await isPostingFeedEnabled();
      const widget = await getHtmlWidget({
        widget: { parent_id: groupData.id, parent_type: 'Team', context: 'team' }
      });
      setGroupDetails({
        ...groupData,
        enablePostingFeed: groupData.enablePostingFeed && postingFeedEnabled,
        widget: widget?.widgets?.length ? widget?.widgets[0] : {},
        userBelongsToGroup:
          groupData.isMember ||
          groupData.isTeamAdmin ||
          groupData.isTeamSubAdmin ||
          groupData.isTeamModerator
      });
      if (showLeaderBoardToMember) {
        getLeaderBoardData(groupData.id);
      }
    } else {
      setGroupDetails({});
    }
  };

  const getLink = (defaultLabel, id) => {
    const link = getTabPath(defaultLabel, id);
    if (defaultLabel.toLowerCase() === 'home') {
      return `${prefix}`;
    } else return `${prefix}/${link.replaceAll(SPECIAL_CHAR_REGEX, '')}`;
  };

  const getLeaderBoardData = groupId => {
    let payload = {
      period: 'all_time',
      show_my_result: true,
      order_attr: 'smartbites_score',
      limit: 5,
      offset: 0,
      'group_ids[]': [groupId],
      fields:
        'id,name,handle,job_title,bio,channel_count,expert_topics,followers_count,following,group_count,order_by_score,picture,roles,roles_default_names'
    };
    if (LD.leaderboardEsDatasource()) {
      payload.version = 'v3';
    }
    setIsLeaderBoardLoading(true);
    getUserScores(payload)
      .then(response => {
        const { currentUser, users, totalCount } = response;
        setLeaderBoardUsers(users);
        setCurrentUserLeaderBoardStat(currentUser);
        setTotalLeaderBoardUsers(totalCount);
        setIsLeaderBoardLoading(false);
      })
      .catch(error => {
        console.error('Error in leader board response', error);
        setIsLeaderBoardLoading(false);
      });
  };

  const reloadTheContent = () => {
    setFilterByLanguage(!filterByLanguage);
    setIsContentIsReloading(true);
    setTimeout(() => {
      setIsContentIsReloading(false);
    }, 0);
  };

  const fetchPendingUsersCount = () => {
    getUsersInAGroupV2(groupDetails.id, {
      limit: 0,
      offset: 0
    })
      .then(response => {
        setPendingUsersCount(response.pending.total);
      })
      .catch(error => {
        return error;
      });
  };

  const getAdpUser = () => {
    let payload = { email: currentUserEmail, groupId: groupDetails.id };
    getAdpUserStatus(payload)
      .then(data => {
        setIsAdpUser(data.isAdpUser);
      })
      .catch(error => {
        return error;
      });
  };

  const getFeaturedContent = async () => {
    const payload = {
      pin: { pinnable_id: groupDetails.id, pinnable_type: 'Team' },
      filter_by_language: true
    };

    try {
      let response = await fetchPinnedcards(payload);
      let cards = response.pins;

      if (isLmsProviderEnabledInstance()) {
        cards = await getLMSAvailability(cards);
      }

      const featuredCardIdList = cards.map(obj => obj.id);
      setFeaturedCardIds(featuredCardIdList);

      setFeaturedCards(cards);
      setTotalFeaturedCards(cards.length);
      setIsFeaturedContentIsLoading(false);
    } catch (error) {
      console.error(
        `Error in GroupConsumptionProvider.getFeaturedContent.fetchPinnedcards ${error}`
      );
    }
  };

  const declineGroupInvitationModalHandler = (groupId, groupName) => {
    setIsDeclineInvitationModalOpen(!isDeclineInvitationModalOpen);
    setInvitationGroupId(groupId);
    setInvitationGroupName(groupName);
  };

  const updateGroupDetails = async () => {
    const groupData = await fetchGroupDetails(slug, dispatch);
    const postingFeedEnabled = await isPostingFeedEnabled();
    setGroupDetails({
      ...groupData,
      enablePostingFeed: groupData.enablePostingFeed && postingFeedEnabled,
      userBelongsToGroup:
        groupData.isMember ||
        groupData.isTeamAdmin ||
        groupData.isTeamSubAdmin ||
        groupData.isTeamModerator
    });
  };

  const declineGroupInviteRequest = () => {
    dispatch(declineInviteToGroup(invitationGroupId))
      .then(() => {
        updateGroupDetails();
        setIsDeclineInvitationModalOpen(!isDeclineInvitationModalOpen);
      })
      .catch(error => {
        setIsDeclineInvitationModalOpen(!isDeclineInvitationModalOpen);
        console.error(`Error in declineGroupInviteRequest.declineInviteToGroup fun ${error}`);
      });
  };

  useEffect(() => {
    const userProperties = [
      'followingChannels',
      'roles',
      'rolesDefaultNames',
      'writableChannels',
      'first_name',
      'last_name'
    ];
    updateUserInfo(userProperties, isCurrentUserId);
    loadInitialData();
  }, []);

  useEffect(() => {
    if (groupDetails !== null && slug) {
      fetchPendingUsersCount();
      getFeaturedContent();

      if (LD.adpFlag()) {
        getAdpUser();
      }
    }
  }, [groupDetails]);

  const toggleFeaturedStatus = data => {
    const cardId = data.card.id;
    const featuredPayload = {
      pinnable_id: groupDetails.id,
      pinnable_type: 'Team',
      object_id: cardId,
      object_type: 'Card'
    };
    const isAlreadyFeatured = data.isFeaturedCard;
    const previousFeaturedCard = [...featuredCards];
    const previousFeaturedCardIds = [...featuredCardIds];
    if (isAlreadyFeatured) {
      unpinCard(featuredPayload)
        .then(() => {
          const cardIndex = previousFeaturedCard.findIndex(obj => obj.id === cardId);
          const cardIdIndex = previousFeaturedCardIds.indexOf(cardId);
          previousFeaturedCard.splice(cardIndex, 1);
          previousFeaturedCardIds.splice(cardIdIndex, 1);
          setFeaturedCardIds(previousFeaturedCardIds);
          setFeaturedCards(previousFeaturedCard);
          dispatch(
            openSnackBar(
              translatr(
                'web.group.main',
                'CardHasBeenSuccessfullyRemovedFromFeaturedContentSection'
              )
            )
          );
        })
        .catch(error => {
          console.error(
            `Error in GroupConsumptionProvider.toggleFeaturedStatus.unpinCard ${error}`
          );
        });
    } else {
      if (previousFeaturedCard.length < 10) {
        pinCard(featuredPayload)
          .then(() => {
            previousFeaturedCard.unshift(data.card);
            previousFeaturedCardIds.unshift(cardId);
            setFeaturedCardIds(previousFeaturedCardIds);
            setFeaturedCards(previousFeaturedCard);
            dispatch(
              openSnackBar(
                translatr('web.group.main', 'CardHasBeenSuccessfullyAddedToFeaturedContentSection')
              )
            );
          })
          .catch(error => {
            console.error(
              `Error in GroupConsumptionProvider.toggleFeaturedStatus.pinCard ${error}`
            );
          });
      } else {
        dispatch(
          openSnackBar(
            translatr('web.group.main', 'YouCanNotMarkedMoreThan10FeaturedCardsInAGroup'),
            'error'
          )
        );
      }
    }
  };

  const gotoLeaderBoard = event => {
    event.preventDefault();
    event.stopPropagation();
    navigate(`/leaderboard?group_id=${groupDetails.id}`);
  };

  const providerObject = {
    groupDetails,
    isCurrentUserIsAdmin,
    isPermissionOfOrgAdmin: Permissions.has('ADMIN_ONLY'),
    isCurrentUserSuperAdmin,
    isCurrentUserId,
    groupTabs,
    featuredCards,
    setFeaturedCards,
    totalFeaturedCards,
    toggleFeaturedStatus,
    featuredCardIds,
    dispatch,
    isGroupEditable,
    pendingUsersCount,
    hideMembersGroupList,
    leaderboard,
    isAdpUser,
    isContentIsReloading,
    reloadTheContent,
    prefix,
    isFeaturedContentIsLoading,
    leaderBoardUsers,
    currentUserLeaderBoardStat,
    totalLeaderBoardUsers,
    gotoLeaderBoard,
    orgId,
    isTeamActivityVisible,
    editPageHistory,
    setEditPageHistory,
    declineGroupInvitationModalHandler,
    invitationGroupName,
    invitationGroupId,
    declineGroupInviteRequest,
    isDeclineInvitationModalOpen,
    updateGroupDetails,
    showLeaderBoardToMember,
    isLeaderboardLoading,
    filterByLanguage,
    showPlareUI: theme === ThemeId.PLARE
  };

  return (
    <GroupConsumptionContext.Provider value={providerObject}>
      {groupDetails ? children : null}
    </GroupConsumptionContext.Provider>
  );
};

GroupConsumptionProvider.propTypes = {
  children: node,
  isCurrentUserIsAdmin: string,
  isCurrentUserSuperAdmin: string,
  updateUserInfo: func,
  isCurrentUserId: string,
  openFeaturedCardsModal: func,
  dispatch: func,
  hideMembersGroupList: bool,
  leaderboard: bool,
  currentUserEmail: string,
  isTeamActivityVisible: bool,
  allLangs: array,
  currentUserLang: string,
  currentAppLang: string,
  assignmentLabel: object,
  orgId: number,
  theme: string
};

const mapStoreStateToProps = ({ currentUser, team, theme }) => {
  const configs = team?.get('config');
  const isTeamActivityVisible = team?.get('OrgConfig')?.leftRail?.['web/leftRail/teamActivity']
    ?.visible;

  return {
    isCurrentUserIsAdmin: currentUser?.get('isAdmin'),
    isCurrentUserSuperAdmin: currentUser?.get('isSuperAdmin'),
    isCurrentUserId: currentUser?.get('id'),
    hideMembersGroupList: configs?.hide_members_group_list,
    leaderboard: configs?.leaderboard,
    orgId: team?.get('orgId'),
    currentUserEmail: currentUser?.get('email'),
    isTeamActivityVisible,
    allLangs: team.get('languages'),
    currentUserLang: currentUser.get('profile').get('language'),
    currentAppLang: team.get('currentAppLanguage'),
    assignmentLabel: team?.get('Feed')?.['feed/myAssignments'],
    theme: theme?.get('themeId')
  };
};

const mapDispatchToProps = dispatch => {
  return {
    updateUserInfo: getSpecificUserInfo,
    dispatch
  };
};

export default connect(mapStoreStateToProps, mapDispatchToProps)(GroupConsumptionProvider);
