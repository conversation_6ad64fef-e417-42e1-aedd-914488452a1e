import React, { useState } from 'react';
import Card from 'centralized-design-system/src/Card/CardContainer';
import { translatr } from 'centralized-design-system/src/Translatr';
import SeparatorLine from '../components/SeparatorLine';
import { CohortCreationStep, CohortFormData, CohortFormErrors, StepType } from '../types';
import CohortCreationFormButtons from './CohortCreationFormButtons';
import CohortCreationFormNavigation from './CohortCreationFormNavigation';
import Backarrow from '@components/backarrow';
import "./CohortCreation.scss";
import { isEmpty } from 'lodash';
import StepContainer from './steps/StepContainer';
import { useNavigate } from 'react-router-dom';

const obtainInitialFormData = (): CohortFormData => {
  return {
    name: "",
    description: "",
    startDate: undefined,
    endDate: undefined,
    active: true
  }
}

const CohortCreationForm = () => {
  const navigate = useNavigate();

  const [formData, setFormData] = useState<CohortFormData>(obtainInitialFormData());
  const [errors, setErrors] = useState<CohortFormErrors>({});
  const [showErrors, setShowErrors] = useState<boolean>(false);
  const [activeStep, setActiveStep] = useState<StepType>('basic-details');

  const NAME_REQUIRED_ERROR_MESSAGE = "Name is required" /* todo: translate */
  const DATE_IS_REQUIRED = "This field is required" /* todo: translate */

  const validateBasicDetails = (cohortFormData: CohortFormData): CohortFormErrors => {
    const errors: CohortFormErrors = {};
    if (isEmpty(cohortFormData.name?.trim())) errors.name = NAME_REQUIRED_ERROR_MESSAGE;
    if (isEmpty(cohortFormData.startDate)) errors.startDate = DATE_IS_REQUIRED;
    if (isEmpty(cohortFormData.endDate)) errors.endDate = DATE_IS_REQUIRED;
    return errors;
  }

  const focusOnFirstErroredField = (stepErrors: CohortFormErrors) => {
    const fieldWithError = Object.keys(stepErrors).find(key => stepErrors[key as keyof CohortFormErrors]);

    if (fieldWithError) {
      const element = document.getElementById(fieldWithError) || document.querySelector(`[name="${fieldWithError}"]`);
      element?.focus?.();
    }
  }

  const creationSteps: Array<CohortCreationStep> = [
    {
      navigationId: "basic-details",
      title: "Basic details", // todo: translate
      description: "Below you can determine the basic details for your Cohort.", // todo: translate
      validationFunction: validateBasicDetails
    },
    {
      navigationId: "content",
      title: "Content", // todo: translate
      description: "Below you can create, search and organize the learning content of the Cohort. All the new content created here will also be available to you on the platform.", // todo: translate
    }
  ];
  const currentStepIndex = creationSteps.findIndex(step => step.navigationId === activeStep);

  const isLastStep = () => currentStepIndex === creationSteps.length - 1;
  const isFirstStep = () => currentStepIndex === 0;

  const handleNextStep = () => {
    const currentStep = creationSteps[currentStepIndex];

    const stepErrors = currentStep.validationFunction(formData);
    if(Object.keys(stepErrors).length > 0) {
      setErrors(stepErrors);
      setShowErrors(true);
      focusOnFirstErroredField(stepErrors);
      return;
    }

    if (!isLastStep()) {
      setActiveStep(creationSteps[currentStepIndex + 1].navigationId as StepType);
    } else {
      console.log('Form completed with data:', formData);
    }
  };

  const handlePreviousStep = () => {
    if (!isFirstStep()) {
      setActiveStep(creationSteps[currentStepIndex - 1].navigationId as StepType);
    } else {
      navigate(-1);
    }
  };

  const handleUpdateForm = (name: keyof CohortFormData, newValue: any) => {
    const newData = {
      ...formData,
      [name]: newValue
    }

    const currentStep = creationSteps[currentStepIndex];

    setFormData(newData)
    if(currentStep.validationFunction) {
      setErrors(currentStep.validationFunction(newData));
    }
  }

  return (
    <main className="ed-ui cohort-creation_container">
      <Backarrow
        label={translatr('web.common.main', 'Back')}
        additionalClasses={'cohort-creation_back-button'}
      />
      <Card additionalClasses={['cohort_creation_form_navigation']}>
        <CohortCreationFormNavigation
          activeStep={activeStep}
          creationSteps={creationSteps}
        />
      </Card>
      <Card additionalClasses={['cohort-creation_form']}>
        <StepContainer
          activeStep={activeStep}
          errors={errors}
          formData={formData}
          handleUpdateForm={handleUpdateForm}
          showErrors={showErrors}
        />
        <SeparatorLine />
        <CohortCreationFormButtons
          positiveButtonLabel={isLastStep() ? "Create Cohort" : "Next step"}
          positiveButtonClick={handleNextStep}
          neutralButtonLabel={isFirstStep() ? "Cancel" : "Prev step"}
          neutralButtonClick={handlePreviousStep}
        />
      </Card>
    </main>
  );
};

export default CohortCreationForm;
