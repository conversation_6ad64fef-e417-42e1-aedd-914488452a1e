import React from 'react';
import { CohortCreationStep, StepType } from '@pages/group/cohorts/types';

interface CohortCreationFormNavigationProps {
  creationSteps: Array<CohortCreationStep>,
  activeStep?: StepType;
}

const CohortCreationFormNavigation: React.FC<CohortCreationFormNavigationProps> = ({
  creationSteps,
  activeStep = 'basic-details'
}) => {

  const activeStepIndex = creationSteps.findIndex(step => step.navigationId === activeStep);

  const activeStepDescription = creationSteps[activeStepIndex].description;
  return (
    <>
      <h2>Create Cohort</h2> {/* todo: translate */}
      <div className="steps-container">
        {creationSteps.map((step, index) => {
          const isActive = index === activeStepIndex;
          const isCompleted = index < activeStepIndex;

          let stepClass = '';
          if (isActive) stepClass = 'active';
          else if (isCompleted) stepClass = 'completed';

          return (
            <React.Fragment key={step.navigationId}>
              <div className={`step-item ${stepClass}`}>
                <div className="step-number">{index + 1}</div>
                <div className="step-title">{step.title}</div>
              </div>

              {index < creationSteps.length - 1 && (
                <div className="step-separator">
                  <div className="separator-line"></div>
                </div>
              )}
            </React.Fragment>
          );
        })}
      </div>
      <div>{activeStepDescription}</div>
    </>
  );
};

export default CohortCreationFormNavigation;
