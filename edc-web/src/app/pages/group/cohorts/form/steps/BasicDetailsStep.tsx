import React, { useCallback } from 'react';
import TextField from 'centralized-design-system/src/Inputs/TextField';
import { AreaInput } from 'centralized-design-system/src/Inputs';
import DatePickerField from '@components/DatePicker/DatePickerField';
import { convertToDateString } from '@pages/group/cohorts/utils';
import Switch from 'centralized-design-system/src/Switch';
import { CohortFormData, CohortFormErrors } from '@pages/group/cohorts/types';
import SeparatorLine from '@pages/group/cohorts/components/SeparatorLine';

interface BasicDetailsStepProps {
  errors: CohortFormErrors,
  data: CohortFormData,
  handleUpdateForm(name: keyof CohortFormData, value: any): void
  showErrors: boolean,
}

const BasicDetailsStep: React.FC<BasicDetailsStepProps> = ({ errors, data, handleUpdateForm, showErrors }) => {

  const handleDateChange = useCallback((fieldName: keyof CohortFormData) => (picked: Date | null) => {
    handleUpdateForm(fieldName, convertToDateString(picked));
  }, [handleUpdateForm]);

  return (
    <div className="cohort-form">
      <SeparatorLine label={"Basic information"} />
      <TextField
        id="name"
        title={"Name"} /* todo: translate */
        placeholder={"Enter cohort name"} /* todo: translate */
        defaultValue={data.name}
        setValue={(newVal: string) => handleUpdateForm("name", newVal)}
        error={showErrors && errors?.name}
        isTranslated
        required
        shouldCheckForMaxChar
        maxLen={150}
      />
      <AreaInput
        title={"Description"}
        placeholder={"Describe this cohort"}
        defaultTextInput={data.description}
        setValue={(newVal: string) => handleUpdateForm("description", newVal)}
        isTranslated
        optional
        shouldCheckForMaxChar
        maxLen={2000}
      />
      <SeparatorLine label={"Time & Duration"} />
      <div className='ed-input-container'>
        <DatePickerField
          id={"startDate"}
          label={"Cohort start"}
          required
          error={showErrors && errors?.startDate}
          date={data?.startDate ? new Date(data.startDate) : null}
          maxDate={data?.endDate ? new Date(data.endDate) : null}
          onChange={handleDateChange('startDate')}
        />
        <DatePickerField
          id={"endDate"}
          label={"Cohort end"}
          required
          error={showErrors && errors?.endDate}
          date={data?.endDate ? new Date(data.endDate) : null}
          minDate={data?.startDate ? new Date(data.startDate) : null}
          onChange={handleDateChange('endDate')}
        />
      </div>
      <SeparatorLine label={"Settings"} />
      <div className='cohort-form_switch'>
        <Switch
          name={"Active cohort"}
          defaultChecked={data?.active}
          onChange={(e) => handleUpdateForm("active", e.target.checked)}
        />
      </div>
    </div>
  );
};

export default BasicDetailsStep;
