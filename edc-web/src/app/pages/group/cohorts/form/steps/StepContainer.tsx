import React from 'react';
import BasicDetailsStep from '@pages/group/cohorts/form/steps/BasicDetailsStep';
import ContentStep from '@pages/group/cohorts/form/steps/ContentStep';

const StepContainer = ({ activeStep, errors, formData, handleUpdateForm, showErrors }: any) => {
  switch (activeStep) {
    case 'basic-details':
      return (
        <BasicDetailsStep
          errors={errors}
          data={formData}
          handleUpdateForm={handleUpdateForm}
          showErrors={showErrors}
        />
      );
    case 'content':
      return (
        <ContentStep />
      );
    default:
      throw new Error(`Unknown step: ${activeStep}`);
  }
};

export default StepContainer;
