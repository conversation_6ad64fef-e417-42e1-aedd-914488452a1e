import { Button } from 'centralized-design-system/src/Buttons';
import React from 'react';

interface CohortCreationFormButtonsProps {
  positiveButtonLabel: string,
  positiveButtonClick(): void,
  neutralButtonLabel: string,
  neutralButtonClick(): void,
}

const CohortCreationFormButtons: React.FC<CohortCreationFormButtonsProps> = ({ positiveButtonLabel, positiveButtonClick, neutralButtonLabel, neutralButtonClick }) => {
  return (
    <div className="cohort-creation_buttons">
      <Button color="secondary" variant="ghost" onClick={neutralButtonClick}>{neutralButtonLabel}</Button>
      <Button color="primary" onClick={positiveButtonClick}>{positiveButtonLabel}</Button>
    </div>
  );
};

export default CohortCreationFormButtons;
