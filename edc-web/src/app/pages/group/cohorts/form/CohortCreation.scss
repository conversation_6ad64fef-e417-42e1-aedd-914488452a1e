@import '~centralized-design-system/src/Styles/_variables.scss';

.cohort-creation_container {
  display: flex;
  flex-direction: column;
  gap: var(--ed-spacing-base);
  width: 98%;
  margin: auto;

  .cohort-creation_back-button {
    align-self: baseline;
  }

  .cohort_creation_form_navigation {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;

    .steps-container {
      @media (min-width: 768px) {
        min-width: 640px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
      }
    }

    .step-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      min-width: 102px;
      gap: 4px;

      &.active {
        .step-number {
          background-color: transparent;
          color: var(--ed-primary-base);
          border: 2px solid var(--ed-primary-base);
        }
      }

      &.completed {
        .step-number {
          background-color: var(--ed-primary-base);
          color: white;
          border: none;
        }
      }

      .step-number {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: var(--ed-neutral-6);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: var(--ed-font-size-lg);
        font-weight: var(--ed-font-weight-black);
        margin-bottom: var(--ed-spacing-2xs);
      }

      .step-title {
        text-align: center;
      }
    }

    .step-separator {
      display: flex;
      justify-content: center;
      width: 100%;
      padding: var(--ed-spacing-2xs) 0;

      @media (min-width: 768px) {
        padding: 0 var(--ed-spacing-2xs);
      }

      .separator-line {
        width: 2px;
        height: 24px;
        background-color: var(--ed-neutral-6);

        @media (min-width: 768px) {
          width: 100%;
          height: 1px;
          margin-top: var(--ed-spacing-base);
        }
      }
    }
  }

  .cohort-creation_form {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .cohort-form {
      display: flex;
      flex-direction: column;
      min-width: 620px;
      max-width: 620px;

      &_switch {
        display: flex;
        padding: var(--ed-spacing-base) 0;

        .switch {
          padding: 0;
        }
      }
    }
  }

  .cohort-creation_buttons {
    padding: var(--ed-spacing-base);
  }
}
