export interface CohortCreationStep {
  navigationId: StepType,
  title: string,
  description: string,
  validationFunction?: (formData: CohortFormData) => CohortFormErrors
}

export type StepType = 'basic-details' | 'configuration' | 'content';

export interface CohortFormData {
  name: string,
  description: string,
  startDate: Date | undefined,
  endDate: Date | undefined,
  active: boolean,
}

export interface CohortFormErrors {
  name?: string,
  startDate?: string,
  endDate?: string,
}
