.cohorts {
  &_tab-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--ed-spacing-xl);

    .ed-input-container {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      gap: var(--ed-spacing-base);

      label {
        margin: 0;
        font-weight: var(--ed-font-weight-bold);
        font-size: 20px;
      }
    }
  }

  .cohorts_container {
    flex-direction: row;
    display: flex;
    gap: var(--ed-spacing-xl);

    &_left-navigation {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: var(--ed-spacing-4xs);

      &_element {
        display: flex;
        flex-direction: row;
        gap: var(--ed-spacing-4xs);

        &.active-navigation {
          .cohorts_left-navigation_element_label {
            background-color: var(--ed-primary-5);
          }
        }

        .indicator {
          height: 44px;
          width: 3px;
          border-radius: 2px;

          &.active {
            background: var(--ed-primary-base);
          }
        }

        &_label {
          width: 100%;
          font-weight: var(--ed-font-weight-bold);
          padding: var(--ed-spacing-xs);
          border-radius: var(--ed-border-radius-md);

          &:hover {
            cursor: pointer;
            background-color: var(--ed-primary-5);
          }
        }
      }
    }

    &_page-content {
      flex: 3;
    }
  }
}
