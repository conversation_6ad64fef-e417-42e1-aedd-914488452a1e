import React from 'react';
import cn from 'classnames';
import { useLocation } from 'react-router-dom';
import Card from 'centralized-design-system/src/Card/CardContainer';
import CohortCreationForm from './form/CohortCreationForm';
import LearningPage from './pages/LearningPage';
import MembersPage from './pages/MembersPage';
import CohortTopNavigation from './CohortTopNavigation';
import "./Cohorts.scss";

const Cohorts = () => {
  const [activeNavigation, setActiveNavigation] = React.useState('learning');

  const location = useLocation();

  const isCreationMode = location.pathname.endsWith('/create');

  if (isCreationMode) {
    return <CohortCreationForm />;
  }

  const cohortNavigationItems = [
    {
      id: "learning",
      label: "Learning",
      component: <LearningPage />
    },
    {
      id: "members",
      label: "Members",
      component: <MembersPage />
    }
  ];

  const selectedNavigationContainer = cohortNavigationItems.find(item => item.id === activeNavigation).component;

  return (
    <div className="cohorts">
      <CohortTopNavigation />
      <div className="cohorts_container">
        <ul className="cohorts_container_left-navigation">
          {cohortNavigationItems.map(item => {
            const isActive = (activeNavigation === item.id);
            return (
              <li
                key={item.id}
                className={cn("cohorts_container_left-navigation_element", {
                  "active-navigation": isActive,
                })}
                onClick={() => setActiveNavigation(item.id)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    setActiveNavigation(item.id);
                  }
                }}
                tabIndex={0}
                role="button"
              >
                <span className={cn("indicator", {"active": isActive})}></span>
                <span className="cohorts_container_left-navigation_element_label">
                  {item.label}
                </span>
              </li>
            );
          })}
        </ul>
        <div className="cohorts_container_page-content">
          <Card>
            {selectedNavigationContainer}
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Cohorts;
