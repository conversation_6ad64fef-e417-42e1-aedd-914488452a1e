import React from 'react';
import { Select } from 'centralized-design-system/src/Inputs';
import { Button } from 'centralized-design-system/src/Buttons';

const CohortTopNavigation = () => {

  const goToEditForm = () => {
    console.log("Edycja!");
  };

  const cohorts = [];

  return (
    <div className="cohorts_tab-navigation">
      {/**
       todo:
       1) add translation
       2) add items (mock?)
       **/}
      <Select
        id="cohort-selector"
        title={"Cohort"}
        items={cohorts}
        defaultValue={cohorts?.length > 0 ? cohorts[0] : null}
        onChange={(newCohort: any) => console.log("handle cohort change") }
        translateDropDownOptions={false}
        isTranslated
      />
      <Button color='primary' variant={'ghost'} onClick={goToEditForm} >
        Settings {/* todo: add translations */}
      </Button>
    </div>
  );
};

export default CohortTopNavigation;
