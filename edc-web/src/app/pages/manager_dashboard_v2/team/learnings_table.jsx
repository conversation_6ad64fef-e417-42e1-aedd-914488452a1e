import React, { useState, useEffect, useRef, useMemo } from 'react';
import PropTypes from 'prop-types';
import Table from 'centralized-design-system/src/Table';
import { SearchInput } from 'centralized-design-system/src/Inputs';
import Pagination from 'centralized-design-system/src/Pagination';
import Loading from 'centralized-design-system/src/Loading';
import { translatr } from 'centralized-design-system/src/Translatr';
import {
  downloadTeamsReport,
  getTeamLearningMatrix
} from 'edc-web-sdk/requests/managerDashboard.v2';
import { debounce } from 'lodash';
import unescape from 'lodash/unescape';
import Tooltip from 'centralized-design-system/src/Tooltip';
import { safeRender } from 'edc-web-sdk/requests/renderingOptions';
import { Button } from 'centralized-design-system/src/Buttons';

import { LearningTableConst } from './LearningTableConst';
import { truncateText } from '@utils/utils';

export const TeamLearningsTable = ({
  contentTableViewHand<PERSON>,
  sendReminderModalHandler,
  user_info_disabled,
  reportsFilter
}) => {
  const [tableData, setTableData] = useState([]);
  const [totalCount, setTotalCount] = useState();
  const [learnings, setLearnings] = useState();
  const [currentPage, setCurrentPage] = useState(0);
  const [loading, setLoading] = useState(true);
  const OFFSET_AMOUNT = 10;
  let searchRef = useRef();

  let optionalHeader = {};

  if (!user_info_disabled) {
    optionalHeader = { send_reminder: translatr('web.manager-dashboard-v2.main', 'SendReminder') };
  }

  const Headers = {
    card_title: translatr('web.manager-dashboard-v2.main', 'ContentName'),
    assigned_total: translatr('web.manager-dashboard-v2.main', 'AssignedTo'),
    not_started: translatr('web.manager-dashboard-v2.main', 'NotStarted'),
    due_in_two_weeks_count: translatr('web.manager-dashboard-v2.main', 'DueIn2Weeks'),
    overdue: translatr('web.common.main', 'Overdue'),
    completed: translatr('web.manager-dashboard-v2.main', 'Completed'),
    ...optionalHeader
  };

  const TableHeaders = Object.keys(Headers).map(header => {
    const theLabel =
      header === 'card_title' ? `${Headers[header]} (${totalCount})` : Headers[header];
    return {
      className: '',
      label: theLabel,
      id: header,
      align: header === 'card_title' || header === 'send_reminder' ? 'text-left' : 'text-center',
      sortable: false
      // onClick: getSortedData
    };
  });

  function doSearch(query) {
    searchRef.current = query;
    getLearnings();
  }

  const debouncedSearch = useMemo(() => debounce(doSearch, 500), []);

  async function getLearnings(page = 1) {
    setLoading(true);
    let pageToRequest = page - 1;
    let q = {
      offset: pageToRequest * OFFSET_AMOUNT,
      limit: 10,
      sort_field: 'card_title',
      sort_direction: 'desc'
    };
    if (searchRef.current) {
      q.q = searchRef.current;
    }
    const reportsFilterParam = reportsFilter?.toLowerCase();
    if (['direct', 'indirect'].includes(reportsFilterParam)) {
      q.reporting_type = reportsFilterParam;
    }
    let teamLearnings = await getTeamLearningMatrix(q).catch(err =>
      console.error('Overview init.getTeamLearningMatrix.func', err)
    );

    if (teamLearnings) {
      setLearnings(teamLearnings.data);
      setTotalCount(teamLearnings.total_count);
      setCurrentPage(page);
      setLoading(false);
    }
  }

  useEffect(() => {
    getLearnings();
  }, []);

  useEffect(() => {
    let TableData = [];
    if (Array.isArray(learnings)) {
      TableData = learnings.map((learning, index) => {
        let row = [];
        Object.keys(Headers).forEach((key, i) => {
          let obj = {
            ...learning,
            overdue: learning.overdue_count
          };
          if (key === 'send_reminder') {
            !user_info_disabled &&
              row.push({
                children:
                  learning.assigned_total == learning.completed ? (
                    <div
                      className="ed-link-secondary none-text-decoration disabled"
                      id={`${key}-${index}-${i}`}
                      aria-labelledby={`${key}-${index}-${i} card_title-${index}-0`}
                    >
                      {translatr('web.manager-dashboard-v2.main', 'AllCompleted')}
                    </div>
                  ) : (
                    <Button
                      color="primary"
                      variant="borderless"
                      padding="xsmall"
                      id={`${key}-${index}-${i}`}
                      aria-labelledby={`${key}-${index}-${i} card_title-${index}-0`}
                      onClick={
                        user_info_disabled ? () => {} : e => sendReminderModalHandler(e, obj)
                      }
                      disabled={user_info_disabled}
                    >
                      {translatr('web.manager-dashboard-v2.main', 'SendReminder')}
                    </Button>
                  ),
                id: `${key}-${index}-${i}`,
                align: 'text-left',
                card_id: obj['card_id'],
                card_title: obj['card_title'],
                onClick: user_info_disabled ? () => {} : contentTableViewHandler
              });
          } else {
            row.push({
              className: key === 'card_title' ? 'card-title' : '',
              children:
                key === 'card_title' ? (
                  <Tooltip
                    isHtmlIncluded
                    message={obj[key]}
                    hide={obj[key]?.length < 32}
                    customClass={!user_info_disabled && 'learning-table-tooltip'}
                  >
                    <Button
                      variant="borderless"
                      color="primary"
                      padding="xsmall"
                      id={`${key}-${index}-${i}`}
                    >
                      {safeRender(
                        obj[key]?.length > 32
                          ? truncateText(unescape(obj[key]), 32, '...')
                          : unescape(obj[key])
                      )}
                    </Button>
                  </Tooltip>
                ) : (
                  <Button variant="borderless" color="secondary" disabled={obj[key] == 0}>
                    {obj[key]}
                  </Button>
                ),
              id: `${key}-${index}-${i}`,
              align: key === 'card_title' ? 'text-left' : 'text-right',
              card_id: obj['card_id'],
              card_title: obj['card_title'],
              onClick: user_info_disabled ? () => {} : contentTableViewHandler
            });
          }
        });
        return row;
      });
    }
    setTableData(TableData);
  }, [learnings]);

  const paginate = resp => {
    let activePage = currentPage;
    if (resp.event === 'next') {
      getLearnings(activePage + 1);
    } else if (resp.event === 'prev') {
      getLearnings(activePage - 1);
    } else {
      if (Number.isInteger(resp.event)) {
        getLearnings(resp.event);
      }
    }
  };

  async function downloadReport(e) {
    let el = e.target;
    el.disabled = true;
    let resp = await downloadTeamsReport();
    el.removeAttribute('disabled');

    let a = document.createElement('a');
    a.href = resp.file_url;
    a.setAttribute('download', 'Team-Report.csv');
    a.click();
  }

  const tooltipMessage = Object.keys(LearningTableConst).map(key => LearningTableConst[key]());

  return (
    <React.Fragment>
      <div className="info-container">
        <h2>{translatr('web.manager-dashboard-v2.main', 'MyTeamsAssignedLearning')}</h2>
        <Tooltip
          message={tooltipMessage.join('<br/>')}
          isHtmlIncluded
          tooltipParentRole="button"
          tabIndex="0"
          ariaLabel={`${translatr('web.manager-dashboard-v2.main', 'Information')}: ${translatr(
            'web.manager-dashboard-v2.main',
            'MyTeamsAssignedLearning'
          )}. ${tooltipMessage.join('')}`}
          customClass="learning-table-tooltip"
        >
          <span class="icon-info-circle font-size-m" />
        </Tooltip>
      </div>
      <div className="flx table-action-container">
        <div className="search-container">
          <SearchInput
            placeholder={translatr('web.manager-dashboard-v2.main', 'SearchContent')}
            onSearch={debouncedSearch}
            searchOnTextChange={true}
          />
        </div>
        <Button
          color="secondary"
          variant="ghost"
          className="ed-btn ed-btn-neutral"
          disabled={totalCount < 1}
          onClick={downloadReport}
        >
          <i className="icon-download" />
          {translatr('web.manager-dashboard-v2.main', 'DownloadReport')}
        </Button>
      </div>

      {loading && (
        <div className="table-loader">
          <Loading />
        </div>
      )}
      <Table
        className={`none-vertical-border ${loading ? 'hide-rows' : ''}`}
        headers={TableHeaders}
        rows={tableData}
      />

      {totalCount > 10 && (
        <div className="mt-16">
          <Pagination
            postPerPage={10}
            totalPosts={totalCount}
            paginate={paginate}
            activePage={currentPage}
            iconType={true}
          />
        </div>
      )}
    </React.Fragment>
  );
};

TeamLearningsTable.propTypes = {
  contentTableViewHandler: PropTypes.func,
  sendReminderModalHandler: PropTypes.func,
  user_info_disabled: PropTypes.bool,
  reportsFilter: PropTypes.string
};
