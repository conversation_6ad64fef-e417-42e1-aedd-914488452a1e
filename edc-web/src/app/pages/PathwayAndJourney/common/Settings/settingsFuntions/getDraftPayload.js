import { ENTITY_TYPE, settingsType } from '../constants';
import { createBadge } from 'edc-web-sdk/requests/badges';
import { getDefaultImage } from 'centralized-design-system/src/Utils/filestack';
import {
  isSelectWeeksOptionSelected,
  getRadioOptionCheckedValue,
  getShareWithAccessIdsFor
} from '../utils';
import uniq from 'lodash/uniq';
import {
  getSelectedProficiencyLevelObj,
  noLevelPlaceholderOption
} from 'centralized-design-system/src/Utils/proficiencyLevels';
import { ENABLE_LX_MEDIA_HUB } from 'centralized-design-system/src/Utils/constants';
import { getLXMediaHubConfigValue } from 'centralized-design-system/src/Utils';

export default async function getDraftPayload({
  description,
  title,
  state,
  nonCuratedChannelIds,
  markAsComplete,
  selectedBadge,
  badgeTitle,
  getBadgeOnCompletionOption,
  cardMetadatum,
  level,
  oldSkillLevel,
  language,
  uniqueCode,
  type,
  shareWith,
  tagsSelected,
  unlockBadgeAfterQuizSuccess,
  privacySettings,
  collaboratorIds = [],
  restrictWith,
  thumbnailObj,
  currentUserId,
  providerName,
  providerThumbNailObj,
  selectedDate,
  journeyTypeOptions,
  skills,
  awardSkills,
  entityFields,
  proficiencyLevels,
  currentUserLang,
  archiveAt,
  excludeFromSearch,
  excludeFromRecommendation
}) {
  const teamsWithPermissionIds = [];
  const usersWithPermissionIds = [];
  const isPathway = type === settingsType.pathway;
  const isManual = getRadioOptionCheckedValue(markAsComplete) === 'manually';
  let channel_ids = getShareWithAccessIdsFor(shareWith, ENTITY_TYPE.channel);
  channel_ids = uniq(channel_ids.concat(nonCuratedChannelIds));
  const payload = {
    message: description || title,
    title: title,
    state: state || 'draft',
    card_type: isPathway ? 'pack' : 'journey',
    card_subtype: isPathway ? 'simple' : getRadioOptionCheckedValue(journeyTypeOptions),
    users_with_access_ids: getShareWithAccessIdsFor(shareWith, ENTITY_TYPE.user),
    team_ids: getShareWithAccessIdsFor(shareWith, ENTITY_TYPE.team),
    channel_ids: channel_ids,
    auto_complete: !isManual,
    is_public: !(privacySettings === 'private') + '', // value has to be in string
    contributor_ids: collaboratorIds,
    entity_fields: entityFields,
    provider: !!providerName ? providerName : '',
    archive_at: archiveAt
  };

  const createNewBadge = async body => {
    try {
      const newBadge = await createBadge(body);
      payload['card_badging_attributes'] = {
        title: badgeTitle,
        badge_id: newBadge.id,
        can_be_assigned: !!getBadgeOnCompletionOption?.[0]?.checked,
        ...(isPathway && {
          all_quizzes_answered: unlockBadgeAfterQuizSuccess?.[0]?.checked
        })
      };
    } catch (error) {
      console.error(`Error in Pathway.getDraftPayload.createBadge.func : ${error}`);
    }
  };

  // badges
  if (selectedBadge?.badgeTemplateId) {
    // This condition is for the creadly badges
    const body = {
      type: 'CardBadge',
      image_url: selectedBadge.filestackUrl ? selectedBadge.filestackUrl : selectedBadge.imageUrl,
      badge_template_id: selectedBadge.badgeTemplateId,
      image_alt_text: selectedBadge.image_alt_text || '',
      source: 'credly'
    };
    await createNewBadge(body);
  } else if (selectedBadge?.id) {
    // This condition is for the S3 bucket badges
    payload['card_badging_attributes'] = {
      title: badgeTitle,
      badge_id: selectedBadge.id,
      can_be_assigned: !!getBadgeOnCompletionOption?.[0]?.checked,
      ...(isPathway && {
        all_quizzes_answered: unlockBadgeAfterQuizSuccess?.[0]?.checked
      })
    };
    payload['badge_details'] = {
      image_alt_text: selectedBadge.image_alt_text || '',
      badge_id: selectedBadge.id
    };
  } else if (selectedBadge?.imageUrl) {
    // This condition is for the new uploaded badges
    const body = {
      type: 'CardBadge',
      image_url: selectedBadge.filestackUrl ? selectedBadge.filestackUrl : selectedBadge.imageUrl,
      image_alt_text: selectedBadge.image_alt_text || ''
    };
    await createNewBadge(body);
  }

  // Languages
  if (language) {
    payload.language = language?.value;
  }

  payload.unique_code = uniqueCode || null;

  // Level
  const levelSelected = getSelectedProficiencyLevelObj(
    level?.value,
    proficiencyLevels,
    currentUserLang
  )?.value;

  if (!!levelSelected) {
    if (cardMetadatum?.id) {
      payload['card_metadatum_attributes'] = {
        id: cardMetadatum.id,
        level: levelSelected
      };
    } else {
      payload['card_metadatum_attributes'] = {
        level: levelSelected
      };
    }
  } else if (cardMetadatum?.id && oldSkillLevel) {
    // Triggered when oldSkill is removed
    payload['card_metadatum_attributes'] = {
      id: cardMetadatum.id,
      level: null
    };
  } else if (level?.value === noLevelPlaceholderOption().value) {
    //Triggered when level selected is no level, since we need to send null in payload for thsi case
    payload['card_metadatum_attributes'] = {
      level: null
    };
  }

  // Tags
  if (tagsSelected) {
    payload.topics = tagsSelected.map(tag => tag.value);
  }

  // Skills
  payload.user_taxonomy_topics = skills || [];
  const payload_cardMetadatum = payload['card_metadatum_attributes'] || {};
  if (!skills?.length) {
    payload['card_metadatum_attributes'] = {
      ...payload_cardMetadatum,
      award_skills: false
    };
  } else {
    payload['card_metadatum_attributes'] = {
      ...payload_cardMetadatum,
      award_skills: awardSkills,
      id: cardMetadatum?.id
    };
  }

  //Exclude Content
  payload['card_metadatum_attributes'] = {
    ...payload['card_metadatum_attributes'],
    exclude_from_search: excludeFromSearch,
    exclude_from_recommendation: excludeFromRecommendation
  };

  //=====restricted to payload
  if (privacySettings === 'private' && restrictWith.length) {
    restrictWith.map(restrictTo => {
      const objType = restrictTo?.type?.toLowerCase();
      if (objType === 'group') {
        teamsWithPermissionIds.push(parseInt(restrictTo.value));
      } else if (objType === 'member') {
        usersWithPermissionIds.push(parseInt(restrictTo.value));
      }
    });

    if (teamsWithPermissionIds.length > 0) {
      payload['teams_with_permission_ids'] = teamsWithPermissionIds;
    }

    if (usersWithPermissionIds.length > 0) {
      payload['users_with_permission_ids'] = usersWithPermissionIds;
    }
  }

  const isLXMediaHubEnabled = getLXMediaHubConfigValue(ENABLE_LX_MEDIA_HUB);

  if (isLXMediaHubEnabled) {
    if (!Object.keys(thumbnailObj).length) {
      payload.media = null;
    }
    if (!!Object.keys(thumbnailObj).length) {
      const { signed_id, alt_text } = thumbnailObj.file;
      payload.media = signed_id;
      payload.media_alt_text = alt_text;
    }
  } else {
    if (!!Object.keys(thumbnailObj).length) {
      payload.filestack = [thumbnailObj.file]; //filestackObj
    } else {
      const defaultImg = getDefaultImage(currentUserId);
      payload.filestack = [defaultImg];
    }
  }

  if (!!providerThumbNailObj) {
    const file = providerThumbNailObj?.file;

    if (isLXMediaHubEnabled) {
      // Only assign the file if it has the 'key' property when isLXMediaHubEnabled is truthy
      if (file?.hasOwnProperty('key')) {
        payload.provider_image = file;
      }
    } else {
      payload.provider_image = file?.url ? file?.url : null;
    }
  }

  if (
    type === settingsType.journey &&
    isSelectWeeksOptionSelected(journeyTypeOptions) &&
    !!selectedDate
  ) {
    payload.journey_start_date = +new Date(selectedDate) / 1000; // As per the Existing App
  }
  return payload;
}
