import { useContext, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { Button } from 'centralized-design-system/src/Buttons';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import { open_v2 as openSnackBar } from '@actions/snackBarActions';
import { createDevPlan, deleteDevPlanById } from 'edc-web-sdk/requests/developmentPlan';
import { useDevPlan } from '../DevelopmentPlanProvider';
import { DevPlanSkill, DevPlanMenuItem } from '../constants';
import MarkAsAspritaionalConfirmationModal from './MarkAsAspritaionalConfirmationModal';
import AspirationsContext from '@pages/TalentMarketplace/shared/AspirationsContext';
import { MAX_ASPIRATIONAL_ROLES } from '@pages/TalentMarketplace/util';
import Tooltip from 'centralized-design-system/src/Tooltip';
import SkeletonLoader from '@components/SkeletonLoader';
import DevPlanChangePathConfirmationModal from '@pages/TalentMarketplace/DevelopmentPlan/modals/DevPlanChangePathConfirmationModal/DevPlanChangePathConfirmationModal';
import DevPlanRemoveConfirmationModal from '@pages/TalentMarketplace/DevelopmentPlan/modals/DevPlanRemoveConfirmationModal/DevPlanRemoveConfirmationModal';
import './DevPlanHeader.scss';
import {
  areAllPhasesCompletedInUnlockedSteps,
  getDefaultCurrentStepId,
  hasStepASkillWithAtLeastOneContent
} from '../utils';
import { removeMemoizedMatchingJobByID } from '@actions/memoizedMatchingJobsActions';
import { JOB_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import Dropdown from 'centralized-design-system/src/Dropdown';

interface DevPlanHeaderProps {
  isDraft: boolean;
}

const DevPlanHeader: React.FC<DevPlanHeaderProps> = ({ isDraft }) => {
  const { isAspirationalRole, aspirations, isAspirationsInitialized } = useContext(
    AspirationsContext
  );
  const saveBtnRef = useRef<HTMLButtonElement>();
  const menuToggleButtonRef = useRef<HTMLButtonElement>();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [showModal, setShowModal] = useState(false);
  const [isChangePathConfirmationModalOpen, setIsChangePathConfirmationModalOpen] = useState(false);
  const [isRemoveConfirmationModalOpen, setIsRemoveConfirmationModalOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [maxAspirationalRolesReached, setMaxAspirationalRolesReached] = useState(false);
  const [isTargetRoleAspirational, setIsTargetRoleAspirational] = useState(false);
  const { data, pathData, isDataLoading, wasPathChanged, isDirectPath } = useDevPlan();
  const targetRole = pathData?.at(-1);
  const mostAdvancedStepId = getDefaultCurrentStepId(data);
  const mostAdvancedStep = data?.steps?.find(({ roleId }) => roleId === mostAdvancedStepId);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const menuItems: DevPlanMenuItem[] = [
    {
      label: translatr('web.talentmarketplace.development-plan', 'MenuRemovePlan'),
      action: () => setIsRemoveConfirmationModalOpen(true)
    }
  ];

  const hasRequiredDataToSave = hasStepASkillWithAtLeastOneContent(mostAdvancedStep);
  const aspirationalLimitReached =
    !isTargetRoleAspirational && isAspirationsInitialized && maxAspirationalRolesReached;
  const allPhasesCompletedInDraft = isDraft && areAllPhasesCompletedInUnlockedSteps(data);

  const saveButtonTooltipMessage = !hasRequiredDataToSave
    ? translatr('web.talentmarketplace.development-plan', 'PlanMustIncludeOneLearingActivity')
    : aspirationalLimitReached
    ? translatr('web.talentmarketplace.main', 'DevPlanLimitWarningConfigurable', {
        max: MAX_ASPIRATIONAL_ROLES,
        tm_aspirational_roles: omp('tm_tm_aspirational_roles')
      })
    : allPhasesCompletedInDraft
    ? translatr('web.talentmarketplace.development-plan', 'AllItemsInActionPlanCompleted', {
        roleName: targetRole?.name
      })
    : '';

  const MenuItemsList: React.FC<{ items: DevPlanMenuItem[] }> = ({ items }) => (
    <ul onClick={e => e.stopPropagation()} role="presentation">
      {items
        .filter(({ hidden }) => !hidden)
        .map((item, index) => (
          <li className="no-padding" key={index}>
            <button
              {...(item.role ? { role: item.role } : {})}
              tabIndex={0}
              onClick={() => {
                item.action();
                setIsMenuOpen(false);
              }}
            >
              {item.label}
            </button>
          </li>
        ))}
    </ul>
  );

  const saveDevelopmentPlan = async () => {
    try {
      setIsSaving(true);
      await dispatch(removeMemoizedMatchingJobByID(JOB_TYPE.ROLE, targetRole?.id));
      await createDevPlan(
        {
          aspirationalRoleId: targetRole?.id,
          path: pathData?.map(({ id }) => id),
          steps: [
            {
              roleId: mostAdvancedStep?.roleId,
              status: mostAdvancedStep?.status,
              phases: [
                {
                  phase: mostAdvancedStep.phases[0]?.phase,
                  status: mostAdvancedStep.phases[0]?.status,
                  skills: mostAdvancedStep.phases[0]?.skills.map((skill: DevPlanSkill) => ({
                    skillId: skill.skillId,
                    skillName: skill.skillName,
                    skillLabel: skill.skillLabel,
                    skillProficiencyLevel: skill.skillProficiencyLevel,
                    contents: skill.contents.map(content => ({
                      id: content.id,
                      externalId: content.externalId
                    }))
                  }))
                }
              ],
              projects: mostAdvancedStep?.projects?.map(({ id }) => ({ id })),
              mentors: mostAdvancedStep?.mentors?.map(({ id }) => ({ id }))
            }
          ]
        },
        //last condition is to handle the case when the user is creating a direct path(backend need to save this as selected path)
        !isTargetRoleAspirational || wasPathChanged || (isTargetRoleAspirational && isDirectPath)
      );
      navigate(`/career/plan/${targetRole?.id}`, {
        state: { isDraft: false, pathData },
        replace: true
      });
      dispatch(
        openSnackBar(
          translatr('web.talentmarketplace.development-plan', 'ActionPlanSavedSuccessfully')
        )
      );
    } catch (e) {
      dispatch(openSnackBar(e?.toString?.(), 'error'));
    }
  };

  const deleteDevelopmentPlan = async () => {
    try {
      const targetRoleId = targetRole?.id;
      if (!targetRoleId) {
        throw new Error('Target role ID is not available');
      }

      await deleteDevPlanById(targetRoleId);
      if (window.history.length > 2) {
        navigate(-1);
      } else {
        navigate(`/career/detail/job_role/${targetRoleId}`);
      }
      dispatch(
        openSnackBar(translatr('web.talentmarketplace.development-plan', 'ActionPlanRemoved'))
      );
    } catch (e) {
      console.error('Error deleting development plan:', e);
      dispatch(
        openSnackBar(
          translatr('web.talentmarketplace.development-plan', 'ActionPlanRemoveFailed'),
          'error'
        )
      );
    }
  };

  const onSave = () => {
    if (!isDraft) return;

    if (isTargetRoleAspirational) {
      if (wasPathChanged) {
        setIsChangePathConfirmationModalOpen(true);
      } else {
        saveDevelopmentPlan();
      }
    } else {
      setShowModal(true);
    }
  };

  const closeModal = () => {
    setShowModal(false);
  };

  useEffect(() => {
    if (isAspirationsInitialized) {
      setMaxAspirationalRolesReached(aspirations.length >= MAX_ASPIRATIONAL_ROLES ? true : false);
      setIsTargetRoleAspirational(Boolean(isAspirationalRole(targetRole?.id)));
    }
  }, [isAspirationsInitialized, targetRole]);

  const isSaveDisabled =
    isDataLoading ||
    isSaving ||
    !isAspirationsInitialized ||
    aspirationalLimitReached ||
    !hasRequiredDataToSave ||
    allPhasesCompletedInDraft;

  return (
    <div className="development-plan-header">
      <h2>
        {isDraft
          ? translatr('web.talentmarketplace.development-plan', 'SaveThisActionPlanPathToGuide')
          : translatr('web.talentmarketplace.development-plan', 'YourJourneyWithThisActionPlan', {
              tm_aspirational_role: omp('tm_tm_aspirational_role')
            })}
      </h2>
      {isDraft && (
        <Tooltip
          message={saveButtonTooltipMessage}
          ariaLabel={saveButtonTooltipMessage}
          hide={!saveButtonTooltipMessage}
          isHtmlIncluded
          customClass={`common-tooltip-setting`}
        >
          {(isDataLoading || !isAspirationsInitialized) && (
            <SkeletonLoader height={36} width={76} />
          )}
          {!isDataLoading && isAspirationsInitialized && (
            <Button
              color="primary"
              ref={saveBtnRef}
              aria-disabled={isSaveDisabled}
              onClick={e => {
                if (isSaveDisabled) {
                  e.preventDefault();
                  e.stopPropagation();
                } else {
                  onSave();
                }
              }}
            >
              {translatr('web.common.main', 'Start')}
            </Button>
          )}
        </Tooltip>
      )}

      {!isDraft && (
        <Dropdown
          ref={menuToggleButtonRef}
          wrapperClass="development-plan-header--menu"
          icon={<i className="icon-ellipsis-h icon card-icon" />}
          ariaLabel={translatr('web.common.main', 'More')}
          setOpenDropdown={setIsMenuOpen}
          clickListenerCB={() => setIsMenuOpen(false)}
          openDropdown={isMenuOpen}
        >
          <MenuItemsList items={menuItems} />
        </Dropdown>
      )}
      {showModal && (
        <MarkAsAspritaionalConfirmationModal
          roleName={targetRole?.name}
          focusReturnElement={saveBtnRef?.current}
          onSave={saveDevelopmentPlan}
          closeModal={closeModal}
        />
      )}
      {isChangePathConfirmationModalOpen && (
        <DevPlanChangePathConfirmationModal
          callback={() => {
            saveDevelopmentPlan();
            setIsChangePathConfirmationModalOpen(false);
          }}
          closeModal={() => setIsChangePathConfirmationModalOpen(false)}
        />
      )}
      {isRemoveConfirmationModalOpen && (
        <DevPlanRemoveConfirmationModal
          focusReturnElement={menuToggleButtonRef?.current}
          callback={() => {
            deleteDevelopmentPlan();
            setIsRemoveConfirmationModalOpen(false);
          }}
          closeModal={() => setIsRemoveConfirmationModalOpen(false)}
        />
      )}
    </div>
  );
};
export default DevPlanHeader;
