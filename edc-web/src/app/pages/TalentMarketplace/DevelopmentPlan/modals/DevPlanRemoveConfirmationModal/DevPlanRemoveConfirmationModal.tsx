import { translatr } from 'centralized-design-system/src/Translatr';
import ConfirmationModal from '@components/modals/ConfirmationModalV2';
import Modal from 'centralized-design-system/src/Modals';
import { useEscapeKey } from '@pages/Sourcing/hooks/useEscapeKey';

interface DevPlanRemoveConfirmationModalProps {
  callback: () => void;
  closeModal: () => void;
  focusReturnElement: HTMLButtonElement;
}

const DevPlanRemoveConfirmationModal: React.FC<DevPlanRemoveConfirmationModalProps> = ({
  focusReturnElement,
  callback,
  closeModal
}) => {
  const handleCloseModal = () => {
    closeModal();
    setTimeout(() => {
      focusReturnElement?.focus();
    }, 50);
  };

  useEscapeKey(handleCloseModal);

  return (
    // @ts-ignore
    <Modal size="small">
      <ConfirmationModal
        message={translatr('web.talentmarketplace.development-plan', 'RemovePlanModalDescription')}
        confirmBtnTitle={translatr('web.common.main', 'Remove')}
        callback={() => callback()}
        cancelClick={handleCloseModal}
        closeModal={handleCloseModal}
        title={translatr('web.talentmarketplace.development-plan', 'RemovePlanModalTitle')}
      />
    </Modal>
  );
};

export default DevPlanRemoveConfirmationModal;
