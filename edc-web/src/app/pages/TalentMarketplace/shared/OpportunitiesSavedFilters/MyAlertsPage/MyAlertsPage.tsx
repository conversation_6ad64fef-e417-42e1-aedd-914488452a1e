import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Skeleton from 'centralized-design-system/src/SkeletonAnimations/Skeleton';
import EmptyState from 'centralized-design-system/src/EmptyStatev2';
import { translatr } from 'centralized-design-system/src/Translatr';
import { ButtonLink } from 'centralized-design-system/src/Buttons';
import { useSavedSearches } from '../hooks';
import { FilterBucketName } from '@actions/savedSearchesActions';
import { BaseHorizontalCard3Columns } from 'centralized-design-system/src/Card/HorizontalCard/HorizontalCard';
import { OpportunitySavedFilter, FilterData, FilterOption } from '../types';
import MyAlertsDeleteModal from './MyAlertsDeleteModal';
import Caution from '../../../../../icons/Caution';
import Tooltip from 'centralized-design-system/src/Tooltip';
import { FILTERS_DEFAULT_ASSOCIATION_ID } from '../../filters/Filters.constants';
import { validateOpportunitySavedSearches } from 'edc-web-sdk/requests/opportunityAlerts';
import './MyAlertsPage.scss';

interface MyAlertsPageProps {
  label: string;
  bucketName: FilterBucketName;
}

/* This page supports currently only job alerts
 backend need to be extended if we want to have alerts for other opportunities */
const MyAlertsPage: React.FC<MyAlertsPageProps> = ({ label, bucketName }) => {
  const navigate = useNavigate();
  const { savedSearches, isSavedSearchesLoading, refreshSavedSearches } = useSavedSearches(
    bucketName
  );
  const [isDeleteAlertModalOpen, setIsDeleteAlertModalOpen] = useState(false);
  const [selectedAlert, setSelectedAlert] = useState<OpportunitySavedFilter | null>(null);
  const [loadingValidations, setLoadingValidations] = useState(false);
  const [validationResults, setValidationResults] = useState<Record<string, any[]>>({});
  const [focusBackCardId, setFocusBackCardId] = useState<string | null>(null);

  const cardRefs = useRef<Record<string, any | null>>({});

  const generateFilterGroups = (filterData: FilterData): string[] => {
    const keyword = filterData?.keyword;
    const filters = filterData?.filters?.[FILTERS_DEFAULT_ASSOCIATION_ID] || {};
    const filterGroups = keyword ? [keyword] : [];

    Object.values(filters).forEach((options: FilterOption[]) => {
      const names: string[] = [];
      options.forEach((option: FilterOption) => {
        names.push(option.name);
      });
      filterGroups.push(names.join(', '));
    });

    return filterGroups;
  };

  const isFilterDeactivated = (id: string) => {
    // Check if the validation results exist for the given ID
    return validationResults?.[id]?.length > 0 || false;
  };

  useEffect(() => {
    const validateSavedSearches = async () => {
      if (!savedSearches?.values) return;
      setLoadingValidations(true);
      const ids = savedSearches.values.map((search: OpportunitySavedFilter) => search.id);

      try {
        const results = await validateOpportunitySavedSearches(ids);
        setValidationResults(results);
      } catch (error) {
        console.error('Error validating saved searches:', error);
      } finally {
        setLoadingValidations(false);
      }
    };

    validateSavedSearches();
  }, [savedSearches]);

  const onDeleteModalClose = () => {
    setIsDeleteAlertModalOpen(false);
    setSelectedAlert(null);
    setTimeout(() => {
      if (cardRefs?.current && focusBackCardId) {
        cardRefs.current[focusBackCardId]?.menuRef?.current?.focus();
      }
    }, 10);
  };

  return (
    <div className="my-alerts__main block">
      <div className="my-alerts__header">
        <h2 className="my-alerts__heading">
          {label} ({savedSearches?.values?.length || 0})
        </h2>
        <ButtonLink color="secondary" variant="ghost" to={'/settings/triggers'}>
          <i className="icon-external-link" />
          {translatr('web.common.main', 'ManageNotifications')}
        </ButtonLink>
      </div>
      {isSavedSearchesLoading ? (
        <>
          <Skeleton width="90%" height={22} />
          <Skeleton width="30%" count={2} />
        </>
      ) : savedSearches?.values?.length > 0 ? (
        <div className="saved-searches-list">
          {savedSearches?.values?.map((savedFilter, index) => {
            const resultsLink = `/career/job-vacancies/all?filterId=${savedFilter?.id}`;
            const filterGroups = generateFilterGroups(savedFilter?.filterCriteria[0]);
            const deactivated = isFilterDeactivated(savedFilter?.id);

            const cardRef = (el: HTMLDivElement | null) => {
              if (el && savedFilter?.id) {
                cardRefs.current[savedFilter.id] = el;
              }
            };

            return (
              <div className="saved-searches-list__item" key={savedFilter?.id}>
                <BaseHorizontalCard3Columns
                  ref={cardRef}
                  id={savedFilter?.id}
                  key={`${savedFilter?.id}-${index}`}
                  link={resultsLink}
                  title={
                    savedFilter?.filterName ? savedFilter.filterName.replace(/[<>"'&]/g, '') : ''
                  }
                  menu={[
                    {
                      label: translatr('web.common.main', 'Delete'),
                      action: () => {
                        setSelectedAlert(savedFilter);
                        setIsDeleteAlertModalOpen(true);
                        setFocusBackCardId(savedFilter?.id || null);
                      }
                    }
                  ]}
                  buttons={[
                    {
                      label: translatr('web.common.main', 'ShowResults'),
                      onClick: () => navigate(resultsLink)
                    }
                  ]}
                >
                  {filterGroups.length > 0 && (
                    <div className="saved-searches-list__item__filter-container">
                      <ul className="saved-searches-list__item__filter-list">
                        {filterGroups.map((filter: string, index: number) => (
                          <li key={index}>
                            {filter}
                            <span
                              className="saved-searches-list__item__filter-vbar"
                              aria-hidden="true"
                            >
                              |
                            </span>
                          </li>
                        ))}
                      </ul>
                      <div className="saved-searches-list__item__filter-monit">
                        {loadingValidations && <Skeleton width="25px" height={25} />}
                        {!loadingValidations && deactivated ? (
                          <Tooltip
                            pos="top"
                            message={translatr('web.talentmarketplace.main', 'AlertIsInactive', {
                              name: savedFilter?.filterName
                            })}
                            isTranslated={true}
                          >
                            <Caution />
                          </Tooltip>
                        ) : null}
                      </div>
                    </div>
                  )}
                </BaseHorizontalCard3Columns>
              </div>
            );
          })}
        </div>
      ) : (
        <EmptyState
          icon="icon-file"
          headingLevel="h3"
          title={translatr('web.talentmarketplace.main', 'NoSavedAlerts')}
          description={translatr('web.talentmarketplace.main', 'NoSavedAlertsDescription')}
        />
      )}
      {isDeleteAlertModalOpen && (
        <MyAlertsDeleteModal
          selectedAlert={selectedAlert}
          callback={() => {
            refreshSavedSearches();
            onDeleteModalClose();
          }}
          closeModal={onDeleteModalClose}
        />
      )}
    </div>
  );
};
export default MyAlertsPage;
