import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import cn from 'classnames';
import TextField from 'centralized-design-system/src/Inputs/TextField';
import { AreaInput, AsyncSearchInput, Select } from 'centralized-design-system/src/Inputs';
import {
  getAllAvailableProficiencyLevels,
  selectLevelPlaceholderOption
} from 'centralized-design-system/src/Utils/proficiencyLevels';
import FileUpload from 'centralized-design-system/src/FileUpload';
import { translatr } from 'centralized-design-system/src/Translatr';
import { useWindowSize } from 'centralized-design-system/src/Utils/hooks';
import { Button } from 'centralized-design-system/src/Buttons';
import { getOcgOrEgt } from '@utils/index';
import { translate, getLevelInArray, onDateChange } from '../../utils';
import { selectCurrentLanguage, selectIsEgtEnabled, selectIsOgcEnabled } from '../../selectors';
import { AttachmentField } from '../../types';
import DatePickerField from '@components/DatePicker/DatePickerField';
import {
  BadgeFormData,
  BadgeFormErrors,
  LevelField
} from '../types';
import { BADGES, CREDENTIALS } from 'centralized-design-system/src/Utils/Uploads/constants';


interface BadgeFormProps {
  errors: BadgeFormErrors,
  data: BadgeFormData,
  handleUpdateForm(name: keyof BadgeFormData, value: any): void
  showErrors: boolean,
  customLabel: string,
}

const BadgeForm: React.FC<BadgeFormProps> = ({ errors, data, handleUpdateForm, showErrors, customLabel }) => {
  const currentUserLang = useSelector(selectCurrentLanguage);
  const isOcgEnabled = useSelector(selectIsOgcEnabled);
  const isEgtEnabled = useSelector(selectIsEgtEnabled);
  // Store initial form data to ensure data and populate available proficiency levels
  const initialFormData = React.useRef(data);

  const { width } = useWindowSize();
  const isDesktopView = width > 500;

  useEffect(() => {
    if(!data.showExpiryDate) {
      handleUpdateForm("expiryDate", undefined);
    }
  }, [data.showExpiryDate])

  const levels = [selectLevelPlaceholderOption(),
    ...getAllAvailableProficiencyLevels(window.__edOrgData.proficiencyLevels, currentUserLang, getLevelInArray(initialFormData.current))];

  const uploadParams = {
    uploadType: BADGES,
    objectType: CREDENTIALS,
    restrictMediaType: { video: false, audio: false }
  };

  return (
    <div className="badge-form">
      <TextField
        id="title"
        title={translate("NameInputTitle")}
        placeholder={translate("BadgeNameInputPlaceholder", { customLabel })}
        defaultValue={data.title}
        setValue={(newVal: string) => handleUpdateForm("title", newVal)}
        error={showErrors && errors?.title}
        isTranslated
        required
        shouldCheckForMaxChar
        maxLen={100}
      />
      <AreaInput
        title={translate('DescriptionInputTitle')}
        placeholder={translate("DescriptionInputPlaceholder")}
        defaultTextInput={data.description}
        setValue={(newVal: string) => handleUpdateForm("description", newVal)}
        isTranslated
        optional
        shouldCheckForMaxChar
        maxLen={200}
      />
      <FileUpload
        key={data.attachment?.file?.key}
        label={translatr('web.common.main', 'IncludeAttachment')}
        description={translatr('web.common.main', 'JpegPngAndPdfUpto2Mb')}
        showMaxSize={false}
        persistedFile={data.attachment?.file}
        previewUrl={data.attachment?.securedUrl}
        showPreviewImage={true}
        setValue={(uploadedFile: AttachmentField) => handleUpdateForm("attachment", uploadedFile)}
        allowedFileType={['application/pdf', 'image/*']}
        scrollTooltipRelativeElement={document.querySelector('.ed-dialog-modal')}
        isTranslated
        optional="optional"
        uploadParams={uploadParams}
      />
      <div className={cn("ed-input-container badge-form_select", { errors: showErrors && errors.level })} >
        <Select
          id="level"
          title={translate('BadgeLevelInputTitle', { customLabel })}
          items={levels}
          defaultValue={levels?.find((level: LevelField) => level?.value === data?.level?.value)?.value}
          required={true}
          onChange={(newLevel: LevelField) => handleUpdateForm("level", newLevel)}
          translateDropDownOptions={false}
          isTranslated
          error={showErrors && errors?.level}
        />
      </div>
      <AsyncSearchInput
        title={translate("AssociatedSkillsInputTitle")}
        placeholder={translate("AssociatedSkillsInputPlaceholder")}
        description={translatr('web.skillspassport.main', `SelectUpTo6BadgeForActiveCrendential`)} //TODO: replace it
        items={data.skills}
        users={false}
        groups={false}
        channels={false}
        topics={true}
        extraPayload={{
          fields: 'id,label,name',
          'taxo_subtypes[]': getOcgOrEgt(isOcgEnabled, isEgtEnabled)
        }}
        extraData={{ currentUserLang }}
        multiselect={true}
        maxLimit={6}
        onChange={(newSkills: Array<Skill>) => {
          handleUpdateForm("skills", newSkills);
        }}
        optional="optional"
      />
      <TextField
        id="issuer"
        title={translate("IssuerInputTitle")}
        placeholder={translate("BadgeIssuerInputPlaceholder")}
        defaultValue={data.issuer}
        setValue={(newVal: string) => handleUpdateForm("issuer", newVal)}
        error={showErrors && errors?.issuer}
        isTranslated
        required
        shouldCheckForMaxChar
        maxLen={200}
      />
      <TextField
        id="badgeId"
        title={translate("BadgeIdInputTitle", { customLabel })}
        placeholder={translate("BadgeIdInputPlaceholder", { customLabel })}
        defaultValue={data.badgeId}
        setValue={(newVal: string) => handleUpdateForm("badgeId", newVal)}
        error={showErrors && errors?.badgeId}
        isTranslated
        shouldCheckForMaxChar
        maxLen={200}
      />
      <TextField
        id="url"
        title={translate("BadgeUrlInputTitle", { customLabel })}
        placeholder={translate("BadgeUrlInputPlaceholder", { customLabel })}
        defaultValue={data.url}
        setValue={(newVal: string) => handleUpdateForm("url", newVal)}
        error={showErrors && errors?.url}
        isTranslated
        optional="optional"
      />
      <div className='ed-input-container badge-form_dates-picker-container'>
        <DatePickerField
          label={translate('IssueDateInputTitle')}
          date={data?.issueDate ? new Date(data.issueDate) : null}
          maxDate={data?.expiryDate ? new Date(data.expiryDate) : null}
          onChange={onDateChange('issueDate', handleUpdateForm)}
        />
        {data.showExpiryDate &&
          <>
            {isDesktopView && <span className="badge-form_dates-delimiter">-</span>}
            <DatePickerField
              label={translate('ExpiryDateInputTitle')}
              date={data?.expiryDate ? new Date(data.expiryDate) : null}
              minDate={data?.issueDate ? new Date(data.issueDate) : null}
              onChange={onDateChange('expiryDate', handleUpdateForm)}
            />
          </>
        }
      </div>
      <div className="ed-input-container">
        <Button
          color="primary"
          variant="borderless"
          size="medium"
          padding="small"
          onClick={() => handleUpdateForm("showExpiryDate", !data.showExpiryDate)}
        >
          {data.showExpiryDate ? `- ${translate("RemoveExpirationDateLabel")}` : `+ ${translate("AddExpirationDateLabel")}`}
        </Button>
      </div>
    </div>
  );
};

export default BadgeForm;
