import React, { useEffect, useState } from 'react';
import DateMonth<PERSON>earField from '@components/DatePicker/DateMonthYearField';
import { translate } from '../../utils';
import moment from 'moment/moment';

interface WorkExperienceDateRangeProps {
  startDate: Date,
  startDateError: string,
  handleStartDateUpdate(date: Date): void
  endDate: Date,
  currentlyWorkingHere: boolean,
  endDateError: string,
  handleEndDateUpdate(date: Date): void
}

type MonthListOption = {
  id: number,
  value: string
}

type YearListOption = {
  id: number,
  value: string
}

const MIN_DATE = moment('1950-01-01').toDate();
const MAX_DATE = moment().toDate();

const YEAR_SELECT_PLACEHOLDER = { id: -1, value: `${translate('YearSelectPlaceholder')}` };
const MONTH_SELECT_PLACEHOLDER = { id: -1, value: `${translate('MonthSelectPlaceholder')}` };

const YEAR_OPTIONS = (() => {
  const minYear = MIN_DATE.getFullYear();
  const maxYear = MAX_DATE.getFullYear();
  const yearsList = Array.from({ length: maxYear - minYear + 1 }, (_, index) => ({
    id: index,
    value: (maxYear - index).toString()
  }))
  yearsList.unshift(YEAR_SELECT_PLACEHOLDER);
  return yearsList;
})();

const getMonthOptions = (chosenYear: YearListOption) => {

  const startMonth = Number(chosenYear?.value) == MIN_DATE?.getFullYear() ? MIN_DATE.getMonth() : 0;
  const endMonth = Number(chosenYear?.value) == MAX_DATE?.getFullYear() ? MAX_DATE.getMonth() : 11;

  const filteredMonths = Array.from({ length: endMonth - startMonth + 1 }, (_, index) => ({
    id: startMonth + index,
    value: new Intl.DateTimeFormat(window.__ED__.profile.language, { month: 'long' }).format(new Date(2000, startMonth + index))
  }));
  filteredMonths.unshift(MONTH_SELECT_PLACEHOLDER);
  return filteredMonths;
};

const findYearInList = (yearsList: Array<YearListOption>, yearToFind: string) => {
  return yearsList.find(year => year.value === yearToFind) || YEAR_SELECT_PLACEHOLDER;
}

const findMonthInList = (monthList: Array<MonthListOption>, monthToFind: number) => {
  return monthList.find(month => month.id == monthToFind) || MONTH_SELECT_PLACEHOLDER;
}

const firstDayOfTheMonth = (year: string, monthIndex: number) => {
  return new Date(Date.UTC(Number(year), monthIndex));
}

const WorkExperienceDateRange = ({
  startDate,
  startDateError,
  handleStartDateUpdate,
  endDate,
  currentlyWorkingHere,
  endDateError,
  handleEndDateUpdate
}: WorkExperienceDateRangeProps) => {


  const [startYear, setStartYear] = useState<YearListOption>(findYearInList(YEAR_OPTIONS, startDate?.getFullYear()?.toString()));
  const [startMonthOpitons, setStartMonthOptions] = useState<Array<MonthListOption>>(getMonthOptions(startYear))
  const [startMonth, setStartMonth] = useState<MonthListOption>(findMonthInList(startMonthOpitons, startDate?.getMonth()));

  const [endYear, setEndYear] = useState<YearListOption>(findYearInList(YEAR_OPTIONS, endDate?.getFullYear()?.toString()));
  const [endMonthOptions, setEndMonthOptions] = useState<Array<MonthListOption>>(getMonthOptions(startYear))
  const [endMonth, setEndMonth] = useState<MonthListOption>(findMonthInList(endMonthOptions, endDate?.getMonth()));

  useEffect(() => {
    if(startYear?.id != -1 && startMonth?.id != -1) {
      handleStartDateUpdate(firstDayOfTheMonth(startYear.value, startMonth.id));
    } else {
      handleStartDateUpdate(undefined);
    }
    const newStartMonthOptions = getMonthOptions(startYear);
    setStartMonthOptions(newStartMonthOptions);
    if(!newStartMonthOptions.find(newMonth => newMonth.id == startMonth.id)) {
      setStartMonth(MONTH_SELECT_PLACEHOLDER);
    }
  }, [startYear, startMonth])

  useEffect(() => {
    if(endYear.id != -1 && endMonth.id != -1) {
      handleEndDateUpdate(firstDayOfTheMonth(endYear.value, endMonth.id));
    } else {
      handleEndDateUpdate(undefined);
    }
    const newEndMonthOptions = getMonthOptions(endYear);
    setEndMonthOptions(newEndMonthOptions);
    if(!newEndMonthOptions.find(newMonth => newMonth.id == endMonth.id)) {
      setEndMonth(MONTH_SELECT_PLACEHOLDER);
    }
  }, [endYear, endMonth])

  useEffect(() => {
    if(currentlyWorkingHere) {
      setEndYear(YEAR_SELECT_PLACEHOLDER);
      setEndMonth(MONTH_SELECT_PLACEHOLDER);
      handleStartDateUpdate(undefined);
    }
  }, [currentlyWorkingHere])

  return (
    <div className="ed-input-container work-experience-form_dates-container">
      <DateMonthYearField
        id="startDate"
        label={translate('StartDateLabel')}
        required
        month={startMonth.id}
        monthOptions={startMonthOpitons}
        onMonthChange={(newMonth) => setStartMonth(findMonthInList(startMonthOpitons, newMonth))}
        year={startYear.value}
        yearOptions={YEAR_OPTIONS}
        onYearChange={(newYear) => setStartYear(findYearInList(YEAR_OPTIONS, newYear))}
        error={startDateError}
      />
      {!currentlyWorkingHere &&
        <DateMonthYearField
          id="endDate"
          label={translate('EndDateLabel')}
          required
          month={endMonth.id}
          monthOptions={endMonthOptions}
          onMonthChange={(newMonth) => setEndMonth(findMonthInList(endMonthOptions, newMonth))}
          year={endYear.value}
          yearOptions={YEAR_OPTIONS}
          onYearChange={(newYear) => setEndYear(findYearInList(YEAR_OPTIONS, newYear))}
          error={endDateError}
        />
      }
    </div>
  );
};

export default WorkExperienceDateRange;
