import React, { useEffect } from 'react';
import moment from 'moment';
import TextField from 'centralized-design-system/src/Inputs/TextField';
import { AreaInput } from 'centralized-design-system/src/Inputs';
import { translatr } from 'centralized-design-system/src/Translatr';
import FileUpload from 'centralized-design-system/src/FileUpload';
import { useWindowSize } from 'centralized-design-system/src/Utils/hooks';
import { Button } from 'centralized-design-system/src/Buttons';
import { onDateChange, translate } from '../../../utils';
import { AttachmentField } from '../../../types';
import { PatentFormData, PatentFormErrors } from '../../types';
import DatePickerField from '@components/DatePicker/DatePickerField';
import { CREDENTIALS, PATENTS } from 'centralized-design-system/src/Utils/Uploads/constants';

interface PatentFormProps {
  errors: PatentFormErrors,
  data: PatentFormData,
  handleUpdateForm(name: keyof PatentFormData, value: any): void
  showErrors: boolean,
}

const PatentForm: React.FC<PatentFormProps> = ({ errors, data, handleUpdateForm, showErrors }) => {
  const { width } = useWindowSize();
  const isDesktopView = width > 500;

  useEffect(() => {
    if(!data.showExpiryDate) {
      handleUpdateForm("expiryDate", undefined);
    }
  }, [data.showExpiryDate])

  const uploadParams = {
    uploadType: PATENTS,
    objectType: CREDENTIALS,
    restrictMediaType: { video: false, audio: false }
  };

  const defaultAllowedImageType = window.__edOrgData.allowedMediaMimeTypes?.image

  return (
    <div className="certification-form">
      <TextField
        id="title"
        title={translate("TitleInputTitle")}
        placeholder={translate("PatentTitleInputPlaceholder")}
        defaultValue={data.title}
        setValue={(newVal: string) => handleUpdateForm("title", newVal)}
        error={showErrors && errors?.title}
        isTranslated
        required
        shouldCheckForMaxChar
        maxLen={50}
      />
      <TextField
        id="nameOfInventors"
        title={translate("PatentNameOfInventorsInputTitle")}
        placeholder={translate("PatentNameOfInventorsInputPlaceholder")}
        defaultValue={data.nameOfInventors}
        setValue={(newVal: string) => handleUpdateForm("nameOfInventors", newVal)}
        error={showErrors && errors?.nameOfInventors}
        isTranslated
        required
        shouldCheckForMaxChar
        maxLen={50}
      />
      <AreaInput
        title={translate("DescriptionInputTitle")}
        placeholder={translate("DescriptionInputPlaceholder")}
        defaultTextInput={data.description}
        setValue={(newVal: string) => handleUpdateForm("description", newVal)}
        isTranslated
        optional
        shouldCheckForMaxChar
        maxLen={500}
      />
      <FileUpload
        key={data.attachment?.file?.key}
        label={translatr('web.common.main', 'IncludeAttachment')}
        description={translatr('web.common.main', 'JpegPngAndPdfUpto2Mb')}
        showMaxSize={false}
        persistedFile={data.attachment?.file}
        previewUrl={data.attachment?.securedUrl}
        showPreviewImage={true}
        setValue={(uploadedFile: AttachmentField) => handleUpdateForm("attachment", uploadedFile)}
        allowedFileType={['application/pdf', ...defaultAllowedImageType]}
        scrollTooltipRelativeElement={document.querySelector('.ed-dialog-modal')}
        isTranslated
        optional="optional"
        uploadParams={uploadParams}
      />
      <TextField
        title={translate("PatentAwardedByInputTitle")}
        placeholder={translate("PatentAwardedByInputPlaceholder")}
        defaultValue={data.awardedBy}
        setValue={(newVal: string) => handleUpdateForm("awardedBy", newVal)}
        isTranslated
        optional="optional"
      />
      <TextField
        id="url"
        title={translate("PatentUrlInputTitle")}
        placeholder={translate("PatentUrlInputPlaceholder")}
        defaultValue={data.url}
        error={showErrors && errors?.url}
        setValue={(newVal: string) => handleUpdateForm("url", newVal)}
        isTranslated
        optional="optional"
      />
      <TextField
        title={translate("PatentApplicationNumberInputTitle")}
        placeholder={translate("PatentApplicationNumberInputPlaceholder")}
        defaultValue={data.applicationNumber}
        setValue={(newVal: string) => handleUpdateForm("applicationNumber", newVal)}
        isTranslated
        optional="optional"
      />
      <TextField
        title={translate("PatentNumberInputTitle")}
        placeholder={translate("PatentNumberInputPlaceholder")}
        defaultValue={data.patentNumber}
        setValue={(newVal: string) => handleUpdateForm("patentNumber", newVal)}
        isTranslated
        optional="optional"
      />
      <div className="ed-input-container">
        <DatePickerField
          label={translate('PatentDateOfPatentInputTitle')}
          date={data?.dateOfPatent ? new Date(data.dateOfPatent) : null}
          maxDate={moment().toDate()}
          onChange={onDateChange('dateOfPatent', handleUpdateForm)}
        />
      </div>
      <div className='ed-input-container certification-form_dates-picker-container'>
        <DatePickerField
          label={translate('PatentFiledOnInputTitle')}
          date={data?.filedOn ? new Date(data.filedOn) : null}
          maxDate={data?.expiryDate ? new Date(data.expiryDate) : moment().toDate()}
          onChange={onDateChange('filedOn', handleUpdateForm)}
        />

        {data.showExpiryDate && (
          <>
            {isDesktopView && (
              <span className="certification-form_dates-delimiter">-</span>
            )}
            <DatePickerField
              label={translate('ExpiryDateInputTitle')}
              date={data?.expiryDate ? new Date(data.expiryDate) : null}
              minDate={data?.filedOn ? new Date(data.filedOn) : null}
              maxDate={
                data?.filedOn
                  ? moment(data.filedOn).add(25, 'years').add(6, 'months').toDate()
                  : moment().add(25, 'years').add(6, 'months').toDate()
              }
              onChange={onDateChange('expiryDate', handleUpdateForm)}
            />
          </>
        )}
      </div>
      <div className="ed-input-container">
        <Button
          color="primary"
          variant="borderless"
          size="medium"
          padding="small"
          onClick={() => handleUpdateForm("showExpiryDate", !data.showExpiryDate)}
        >
          {data.showExpiryDate ? `- ${translate("RemoveExpirationDateLabel")}` : `+ ${translate("AddExpirationDateLabel")}`}
        </Button>
      </div>
    </div>
  );
};

export default PatentForm;
