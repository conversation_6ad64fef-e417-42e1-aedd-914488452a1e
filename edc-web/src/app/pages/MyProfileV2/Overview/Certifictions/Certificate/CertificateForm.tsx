import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import cn from 'classnames';
import moment from 'moment/moment';
import TextField from 'centralized-design-system/src/Inputs/TextField';
import { AreaInput, AsyncSearchInput, Select } from 'centralized-design-system/src/Inputs';
import { translatr } from 'centralized-design-system/src/Translatr';
import {
  getAllAvailableProficiencyLevels,
  getAllAvailableProficiencyLevelsWithNoLevelCheck, selectLevelPlaceholderOption
} from 'centralized-design-system/src/Utils/proficiencyLevels';
import FileUpload from 'centralized-design-system/src/FileUpload';
import { useWindowSize } from 'centralized-design-system/src/Utils/hooks';
import { Button } from 'centralized-design-system/src/Buttons';
import { getOcgOrEgt } from '@utils/index';
import { translate, getLevelInArray, onDateChange } from '../../../utils';
import {
  selectCurrentLanguage,
  selectIsEgtEnabled,
  selectIsOgcEnabled
} from '../../../selectors';
import DatePickerField from '@components/DatePicker/DatePickerField';
import { AttachmentField } from '../../../types';
import {
  CertificateFormData,
  CertificateFormErrors,
  LevelField
} from '../../types';
import { CERTIFICATES, CREDENTIALS } from 'centralized-design-system/src/Utils/Uploads/constants';

interface CertificateFormProps {
  errors: CertificateFormErrors,
  data: CertificateFormData,
  handleUpdateForm(name: keyof CertificateFormData, value: any): void
  showErrors: boolean,
}

const CertificateForm: React.FC<CertificateFormProps> = ({ errors, data, handleUpdateForm, showErrors }) => {
  const currentUserLang = useSelector(selectCurrentLanguage);
  const isOcgEnabled = useSelector(selectIsOgcEnabled);
  const isEgtEnabled = useSelector(selectIsEgtEnabled);
  // Store initial form data to ensure data and populate available proficiency levels
  const initialFormData = React.useRef(data);

  const { width } = useWindowSize();
  const isDesktopView = width > 500;

  useEffect(() => {
    if(!data.showExpiryDate) {
      handleUpdateForm("expiryDate", undefined);
    }
  }, [data.showExpiryDate])

  const levels = [selectLevelPlaceholderOption(),
    ...getAllAvailableProficiencyLevels(window.__edOrgData.proficiencyLevels, currentUserLang, getLevelInArray(initialFormData.current))];


  const uploadParams = {
    uploadType: CERTIFICATES,
    objectType: CREDENTIALS,
    restrictMediaType: { video: false, audio: false }
  };

  return (
    <div className="certification-form">
      <TextField
        id="title"
        title={translate("TitleInputTitle")}
        placeholder={translate("CertificateTitleInputPlaceholder")}
        defaultValue={data.title}
        setValue={(newVal: string) => handleUpdateForm("title", newVal)}
        error={showErrors && errors?.title}
        isTranslated
        required
        shouldCheckForMaxChar
        maxLen={50}
      />
      <AreaInput
        title={translate("DescriptionInputTitle")}
        placeholder={translate("DescriptionInputPlaceholder")}
        defaultTextInput={data.description}
        setValue={(newVal: string) => handleUpdateForm("description", newVal)}
        isTranslated
        optional
        shouldCheckForMaxChar
        maxLen={200}
      />
      <FileUpload
        key={data.attachment?.file?.key}
        label={translatr('web.common.main', 'IncludeAttachment')}
        description={translatr('web.common.main', 'JpegPngAndPdfUpto2Mb')}
        showMaxSize={false}
        persistedFile={data.attachment?.file}
        previewUrl={data.attachment?.securedUrl}
        showPreviewImage={true}
        setValue={(uploadedFile: AttachmentField) => handleUpdateForm("attachment", uploadedFile)}
        allowedFileType={['application/pdf', 'image/*']}
        scrollTooltipRelativeElement={document.querySelector('.ed-dialog-modal')}
        isTranslated
        optional="optional"
        uploadParams={uploadParams}
      />
      <AsyncSearchInput
        title={translate("AssociatedSkillsInputTitle")}
        placeholder={translate("AssociatedSkillsInputPlaceholder")}
        description={translatr('web.skillspassport.main', `SelectUpTo6CertificateForActiveCrendential`)} //TODO: replace it
        items={data.skills}
        users={false}
        groups={false}
        channels={false}
        topics={true}
        extraPayload={{
          fields: 'id,label,name',
          'taxo_subtypes[]': getOcgOrEgt(isOcgEnabled, isEgtEnabled)
        }}
        extraData={{ currentUserLang }}
        multiselect={true}
        maxLimit={6}
        onChange={(newSkills: Array<Skill>) => {
          handleUpdateForm("skills", newSkills);
        }}
        optional="optional"
      />
      <div className={cn("ed-input-container certification-form_select", { errors: showErrors && errors.level })} >
        <Select
          id="level"
          title={translate("CertificateLevelInputTitle")}
          items={levels}
          defaultValue={levels?.find((level: LevelField) => level?.value === data?.level?.value)?.value}
          required={true}
          onChange={(newLevel: LevelField) => handleUpdateForm("level", newLevel)}
          translateDropDownOptions={false}
          isTranslated
          error={showErrors && errors?.level}
        />
      </div>
      <TextField
        id="issuer"
        title={translate("IssuerInputTitle")}
        placeholder={translate("CertificateIssuerInputPlaceholder")}
        defaultValue={data.issuer}
        setValue={(newVal: string) => handleUpdateForm("issuer", newVal)}
        error={showErrors && errors?.issuer}
        isTranslated
        required
        shouldCheckForMaxChar
        maxLen={200}
      />
      <TextField
        title={translate("CertificateIdInputTitle")}
        placeholder={translate("CertificateIdInputPlaceholder")}
        defaultValue={data.certificateId}
        setValue={(newVal: string) => handleUpdateForm("certificateId", newVal)}
        isTranslated
        optional="optional"
        shouldCheckForMaxChar
        maxLen={200}
      />
      <TextField
        id="url"
        title={translate("CertificateUrlInputTitle")}
        placeholder={translate("CertificateUrlInputPlaceholder")}
        defaultValue={data.url}
        error={showErrors && errors?.url}
        setValue={(newVal: string) => handleUpdateForm("url", newVal)}
        isTranslated
        optional="optional"
      />
      <div className='ed-input-container certification-form_dates-picker-container'>
        <DatePickerField
          label={translate('IssueDateInputTitle')}
          date={data?.issueDate ? new Date(data.issueDate) : null}
          maxDate={data?.expiryDate ? new Date(data.expiryDate) : null}
          onChange={onDateChange('issueDate', handleUpdateForm)}
        />
        {data.showExpiryDate && (
          <>
            {isDesktopView && (
              <span className="certification-form_dates-delimiter">-</span>
            )}
            <DatePickerField
              label={translate('ExpiryDateInputTitle')}
              date={data?.expiryDate ? new Date(data.expiryDate) : null}
              minDate={data?.issueDate ? new Date(data.issueDate) : null}
              maxDate={
                data?.issueDate
                  ? moment(data.issueDate).add(10, 'years').toDate()
                  : undefined
              }
              onChange={onDateChange('expiryDate', handleUpdateForm)}
            />
          </>
        )}
      </div>
      <div className="ed-input-container">
        <Button
            color="primary"
            variant="borderless"
            size="medium"
            padding="small"
            onClick={() => handleUpdateForm("showExpiryDate", !data.showExpiryDate)}
        >
          {data.showExpiryDate ? `- ${translate("RemoveExpirationDateLabel")}` : `+ ${translate("AddExpirationDateLabel")}`}
        </Button>
      </div>
    </div>
  );
};

export default CertificateForm;
