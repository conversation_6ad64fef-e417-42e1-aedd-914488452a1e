import React from 'react';
import { useSelector } from 'react-redux';
import cn from 'classnames';
import TextField from 'centralized-design-system/src/Inputs/TextField';
import { AreaInput, AsyncSearchInput, Select } from 'centralized-design-system/src/Inputs';
import { translatr } from 'centralized-design-system/src/Translatr';
import {
  getAllAvailableProficiencyLevels,
  selectLevelPlaceholderOption
} from 'centralized-design-system/src/Utils/proficiencyLevels';
import FileUpload from 'centralized-design-system/src/FileUpload';
import { getOcgOrEgt } from '@utils/index';
import { translate, getLevelInArray, onDateChange } from '../../../utils';
import {
  AssessmentFormData,
  AssessmentFormErrors,
  LevelField
} from '../../types';
import {
  selectCurrentLanguage,
  selectIsEgtEnabled,
  selectIsOgcEnabled
} from '../../../selectors';
import Date<PERSON>icker<PERSON>ield from '@components/DatePicker/DatePickerField';
import moment from 'moment';
import { AttachmentField } from '../../../types';
import { ASSESSMENTS, CREDENTIALS } from 'centralized-design-system/src/Utils/Uploads/constants';

interface AssessmentFormProps {
  errors: AssessmentFormErrors,
  data: AssessmentFormData,
  handleUpdateForm(name: keyof AssessmentFormData, value: any): void
  showErrors: boolean,
}

const AssessmentForm: React.FC<AssessmentFormProps> = ({ errors, data, handleUpdateForm, showErrors }) => {
  const currentUserLang = useSelector(selectCurrentLanguage);
  const isOcgEnabled = useSelector(selectIsOgcEnabled);
  const isEgtEnabled = useSelector(selectIsEgtEnabled);
  // Store initial form data to ensure data and populate available proficiency levels
  const initialFormData = React.useRef(data);

  const levels = [selectLevelPlaceholderOption(),
    ...getAllAvailableProficiencyLevels(window.__edOrgData.proficiencyLevels, currentUserLang, getLevelInArray(initialFormData.current))];


  const uploadParams = {
    uploadType: ASSESSMENTS,
    objectType: CREDENTIALS,
    restrictMediaType: { video: false, audio: false }
  };

  return (
    <div className="certification-form">
      <TextField
        id={"title"}
        title={translate("NameInputTitle")}
        placeholder={translate("AssessmentNameInputPlaceholder")}
        defaultValue={data.title}
        setValue={(newVal: string) => handleUpdateForm("title", newVal)}
        error={showErrors && errors?.title}
        isTranslated
        required
        shouldCheckForMaxChar
        maxLen={50}
      />
      <AreaInput
        title={translate("DescriptionInputTitle")}
        placeholder={translate("DescriptionInputPlaceholder")}
        defaultTextInput={data.description}
        setValue={(newVal: string) => handleUpdateForm("description", newVal)}
        isTranslated
        optional
        shouldCheckForMaxChar
        maxLen={200}
      />
      <FileUpload
        key={data.attachment?.file?.key}
        label={translatr('web.common.main', 'IncludeAttachment')}
        description={translatr('web.common.main', 'JpegPngAndPdfUpto2Mb')}
        showMaxSize={false}
        persistedFile={data.attachment?.file}
        previewUrl={data.attachment?.securedUrl}
        showPreviewImage={true}
        setValue={(uploadedFile: AttachmentField) => handleUpdateForm("attachment", uploadedFile)}
        allowedFileType={['application/pdf', 'image/*']}
        scrollTooltipRelativeElement={document.querySelector('.ed-dialog-modal')}
        isTranslated
        optional="optional"
        uploadParams={uploadParams}
      />
      <div className={cn("ed-input-container certification-form_select", { errors: showErrors && errors.level })} >
        <Select
          id={"level"}
          title={translate("AssessmentLevelInputTitle")}
          items={levels}
          defaultValue={levels?.find((level: LevelField) => level?.value === data?.level?.value)?.value}
          required={true}
          onChange={(newLevel: LevelField) => handleUpdateForm("level", newLevel)}
          translateDropDownOptions={false}
          isTranslated
          error={showErrors && errors?.level}
        />
      </div>
      <AsyncSearchInput
        title={translate("AssociatedSkillsInputTitle")}
        placeholder={translate("AssociatedSkillsInputPlaceholder")}
        description={translatr('web.skillspassport.main', `SelectUpTo6AssessmentForActiveCrendential`)} //TODO: replace it
        items={data.skills}
        users={false}
        groups={false}
        channels={false}
        topics={true}
        extraPayload={{
          fields: 'id,label,name',
          'taxo_subtypes[]': getOcgOrEgt(isOcgEnabled, isEgtEnabled)
        }}
        extraData={{ currentUserLang }}
        multiselect={true}
        maxLimit={6}
        onChange={(newSkills: Array<Skill>) => {
          handleUpdateForm("skills", newSkills);
        }}
        optional="optional"
      />
      <TextField
        id={"issuer"}
        title={translate("IssuerInputTitle")}
        placeholder={translate("AssessmentIssuerInputPlaceholder")}
        defaultValue={data.issuer}
        setValue={(newVal: string) => handleUpdateForm("issuer", newVal)}
        error={showErrors && errors?.issuer}
        isTranslated
        required
        shouldCheckForMaxChar
        maxLen={200}
      />
      <TextField
        id={"score"}
        title={translate("AssessmentScoreInputLabel")}
        placeholder={translate("AssessmentScoreInputPlaceholder")}
        defaultValue={data.score}
        setValue={(newVal: number) => handleUpdateForm("score", newVal)}
        error={showErrors && errors?.score}
        isTranslated
        required
        type="number"
      />
      <TextField
        id="url"
        title={translate("AssessmentUrlInputLabel")}
        placeholder={translate("AssessmentUrlInputPlaceholder")}
        defaultValue={data.url}
        error={showErrors && errors?.url}
        setValue={(newVal: string) => handleUpdateForm("url", newVal)}
        isTranslated
        optional="optional"
      />
      <div className="ed-input-container">
        <DatePickerField
          label={translate('IssueDateInputTitle')}
          date={data?.issueDate ? new Date(data.issueDate) : null}
          maxDate={moment().toDate()}
          onChange={onDateChange('issueDate', handleUpdateForm)}
        />
      </div>
    </div>
  );
};

export default AssessmentForm;
