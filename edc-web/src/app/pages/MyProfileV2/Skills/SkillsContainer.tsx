import React, { useEffect } from 'react';
import { <PERSON><PERSON><PERSON>oa<PERSON> } from 'centralized-design-system/src/MfeLoader';
import { MFE_CONSTANTS } from 'centralized-design-system/src/MfeLoader/mfeConstants';
import { getConfig } from 'centralized-design-system/src/Utils/OrgConfigs';
import TabContainer from '../Components/TabContainer';
import SkillsTabContent from './SkillsTabContent';
import { useParams } from 'react-router-dom';
import { getInitialSkillsData } from './restAction';
import { useSkillsTabData } from '../ProfileProvider';
import { isNewProfileEnabled } from '../utils';
import Loader from '@components/Loader/Loader';

const SkillsContainer = () => {
  const { state: { loading, selfView, currentlyViewedUserExternalId  }, dispatch } = useSkillsTabData();

  const { handle: unformattedHandle } = useParams();
  const userNameOfOpenedProfile = unformattedHandle?.replace('@', '');
  useEffect(() => {
    dispatch({ type: 'SKILLS_TAB_LOADING' });
    getInitialSkillsData(userNameOfOpenedProfile)
      .then((initialData) => {
        dispatch({
          type: 'GET_SKILLS_DATA_SUCCESS',
          ...initialData
        });
      })
      .catch(() => {
        dispatch({
          type: 'GET_SKILLS_DATA_ERROR'
        });
      });
  }, []);

  const shouldLoadCSXSkills = () => {
    const team = JSON.parse(localStorage.getItem('team')) || {};
    const adminConfig = getConfig('OrgCustomizationConfig')?.web;
    return team?.isCsodIntegationEnabled && isNewProfileEnabled() && adminConfig?.enableCSXSkills;
  };

  const getSkillsComponent = () => {
    const userExternalId = currentlyViewedUserExternalId;
    if (loading) return <Loader center />;
    if(shouldLoadCSXSkills()) {
      // If NOT self view display public skill view of the user profile
      return <MfeLoader
          name={'talent-profile'}
          tms={MFE_CONSTANTS.CSX_TMS}
          componentProps={!selfView && userExternalId ? { userExternalId } : undefined}
        />;
    } else {
      return <SkillsTabContent />;
    }
  };

  return (
    <TabContainer>
      {getSkillsComponent()}
    </TabContainer>
  );
};

export default SkillsContainer;
