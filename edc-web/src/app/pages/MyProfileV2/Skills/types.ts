import { AttachmentField, DashboardSectionInfo, SectionInfo } from '../types';

export type ModalType = "addDeclaredSkill" | "deleteDeclaredSkill" | "developingSkill" | "deleteDevelopingSkill" | "filterSkillsEvaluationChart";

export type SkillDashboardInfoSectionName = "My Skills" | "My Developing Skills"

export type ModalAction = "add" | "edit";

export interface SkillsTabState {
  declaredSkills: Skills,
  developingSkills: Skills,
  skillsEvaluation: {
    chartData: Array<SkillsEvaluationChartData>,
    isPrivate: boolean,
    isVisible: boolean,
    label: string,
    error: boolean,
    loading: boolean,
  },
  modals: {
    open: ModalType | undefined;
    action: ModalAction,
    data?: any
  },
  selfView: boolean,
  currentlyViewedUserFullName: string,
  currentlyViewedUserExternalId: string,
  loading: boolean,
  error: boolean,
  dashboardInfoCopy: Array<DashboardSectionInfo>
}

export interface Skills {
  list: Array<Skill>,
  isPrivate: boolean,
  isVisible: boolean,
  canAdd: boolean,
  label: string
}

export interface Skill {
  id?: string,
  skill: SkillField,
  level: LevelField,
  yourExperience?: string,
  description?: string,
  attachment?: AttachmentField,
  manageable: boolean,
}

export interface SkillField {
  id: string,
  label: string,
  name: string,
  topicExternalData?: any,
}

export interface SkillFormErrors {
  skill?: string,
  level?: string
}

export interface LevelField {
  label: string,
  value: string
}

export interface SkillsSectionsInfo {
  declaredSkills: SectionInfo,
  developingSkills: SectionInfo,
  skillsEvaluation: SectionInfo
}

export type SkillsColumnName = "name" | "level";

export type SortDir = "asc" | "desc";

export type SkillsTabAction =
  | { type: 'SKILLS_TAB_LOADING'; }
  | { type: 'GET_SKILLS_DATA_ERROR'; }
  | { type: 'GET_SKILLS_DATA_SUCCESS'; userProfile: any; userSkills: any, skillsEvaluation: any, selfView: boolean, currentlyViewedUserFullName: string, currentlyViewedUserExternalId: string, sections: SkillsSectionsInfo, dashboardInfoCopy: Array<DashboardSectionInfo> }
  | { type: 'OPEN_SKILL_MODAL', modal: ModalType, action?: ModalAction, data?: any }
  | { type: 'CLOSE_SKILL_MODAL' }
  | { type: 'SKILL_UPDATED', payload: any, uuid: string }
  | { type: 'SKILL_CREATED', payload: any }
  | { type: 'SKILL_REMOVED', removedSkillId: string }
  | { type: 'SKILL_EVALUATION_FILTERED', selectedSkills: { [key: string]: boolean } }
  | { type: 'SKILL_EVALUATION_REFRESHING_IN_PROGRESS' }
  | { type: 'SKILL_EVALUATION_REFRESHED', skillsEvaluation: { [key: string]: boolean } }
  | { type: 'SKILL_EVALUATION_REFRESHED_UNCOMPLETED' }
  | { type: 'SKILLS_DEVELOPING_UPDATED', payload: any }
  | { type: 'CHANGED_PRIVATES_OF_DECLARED_SKILLS_SECTION', isPrivate: boolean }
  | { type: 'CHANGED_PRIVATES_OF_DEVELOPING_SKILLS_SECTION', isPrivate: boolean }


export interface SkillsEvaluationChartData {
  id: string,
  skillName: string;
  self: number;
  peer: number;
  recommended: number;
  description: string;
  currentLevel: string,
  targetLevel: string,
  show: boolean;
}

export interface SkillsEvaluationChartPropertiesData {
  name: string,
  opacity: number,
  fill: string,
}

export interface SkillsEvaluationChartProperties {
  self: SkillsEvaluationChartPropertiesData,
  peer: SkillsEvaluationChartPropertiesData,
  recommended: SkillsEvaluationChartPropertiesData,
}
