import {
  SkillsTabState
} from './types';

export const skillsInitialState: SkillsTabState = {
  declaredSkills: {
    list: [],
    isVisible: false,
    canAdd: false,
    isPrivate: false,
    label: ""
  },
  developingSkills: {
    list: [],
    isVisible: false,
    canAdd: false,
    isPrivate: false,
    label: ""
  },
  skillsEvaluation: {
    chartData: [],
    isPrivate: false,
    isVisible: false,
    label: "",
    loading: true,
    error: false
  },
  modals: {
    open: undefined,
    action: "add"
  },
  selfView: false,
  loading: true,
  error: false,
  dashboardInfoCopy: [],
  currentlyViewedUserFullName: undefined,
  currentlyViewedUserExternalId: undefined,
};

