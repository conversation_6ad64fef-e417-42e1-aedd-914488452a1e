import LazyloadComponent from '@components/LazyloadComponent';

export const ComponentLibrary = LazyloadComponent(() => import('./ComponentLibrary'))();
export const Header = LazyloadComponent(() => import('centralized-design-system/src/Header'))();
export const Home = LazyloadComponent(() => import('./home'))();
export const Discover = LazyloadComponent(() => import('./discover'))();
export const ManagerDashboard = LazyloadComponent(() => import('./manager_dashboard'))();
export const ManagerDashboardV2 = LazyloadComponent(() => import('./manager_dashboard_v2'))();
export const ManagerDashboardBlocks = LazyloadComponent(() =>
  import('./manager_dashboard_blocks')
)();
export const ContentDrilldownTable = LazyloadComponent(() =>
  import(
    './manager_dashboard_blocks/tabs/learning/widgets/AssignedLearning/components/ContentDrilldownTable'
  )
)();
export const AnalyticsReports = LazyloadComponent(() => import('./analytics_reports'))();
export const AllGroupStandAlonePage = LazyloadComponent(() => import('./group/all_groups'))();
export const NotificationsContainer = LazyloadComponent(() =>
  import('./notification/NotificationsContainer')
)();
export const ChannelStandalone = LazyloadComponent(() => import('./channel/ChannelStandalone'))();
export const CreateUpdateGroupContainer = LazyloadComponent(() =>
  import('./group/creation/CreateUpdateGroupContainer')
)();

export const GroupManagePosts = LazyloadComponent(() => import('./group/manage_posting'))();

export const AddCohortForm = LazyloadComponent(() => import('./group/cohorts'))();

export const Search = LazyloadComponent(() => import('./search'))();
export const PathwayBaseLayout = LazyloadComponent(() =>
  import('./PathwayAndJourney/Pathway/PathwayBaseLayout')
)();
export const JourneyBaseLayout = LazyloadComponent(() =>
  import('./PathwayAndJourney/Journey/JourneyBaseLayout')
)();

export const CheckinMFE = LazyloadComponent(() => import('./CheckinMFE'))();

export const HomeWrapper = LazyloadComponent(() => import('./home/<USER>'))();

export const ProfileContainer = LazyloadComponent(() =>
  import('./MyProfile/Common/ProfileContainer')
)();
export const ProfilePage = LazyloadComponent(() => import('./MyProfile'))();
export const DashboardInProgressViewAll = LazyloadComponent(() =>
  import('./MyProfile/InProgress/InProgressViewAll')
)();
export const AllActivity = LazyloadComponent(() => import('./MyProfile/Activity/AllActivity'))();

export const SkillsPassportPage = LazyloadComponent(() => import('./SkillsPassport'))();

export const MyContentPage = LazyloadComponent(() => import('./MyContent'))();
export const MyContentPageV2 = LazyloadComponent(() => import('./MyContentV2'))();

export const ExploreByTopics = LazyloadComponent(() => import('./ExploreByTopics'))();

export const SkillCoinsPage = LazyloadComponent(() =>
  import('./MyProfile/SkillCoins/SkillCoins')
)();
export const LearningPlanPage = LazyloadComponent(() => import('./MyLearningPlan'))();
export const SubscriptionPage = LazyloadComponent(() => import('./SubscriptionPage/index'))();
export const SubscriptionPlans = LazyloadComponent(() =>
  import('./SubscriptionPage/SubscriptionPlans')
)();

export const OverviewContainer = LazyloadComponent(() =>
  import('./MyProfileV2/Overview/OverviewContainer')
)();

export const SkillsContainer = LazyloadComponent(() =>
  import('./MyProfileV2/Skills/SkillsContainer')
)();

export const TMPJobVacancyDetailPage = LazyloadComponent(() =>
  import('./TalentMarketplace/DetailPage/JobVacancyDetail')
)();
export const TMPJobVacancyEditPage = LazyloadComponent(() =>
  import('./TalentMarketplace/EditPage/EditPage')
)();
export const TMPJobVacancySkillsPage = LazyloadComponent(() =>
  import('./TalentMarketplace/SkillsPage/SkillsPage')
)();
export const TMDevelopmentPlanPage = LazyloadComponent(() =>
  import('./TalentMarketplace/DevelopmentPlan/DevelopmentPlanPage')
)();
export const TMLandingPageV2 = LazyloadComponent(() =>
  import('./TalentMarketplace/LandingPage/LandingPageV2')
)();
export const PageNotFound = LazyloadComponent(() => import('./PageNotFound'))();

export const Onboarding = LazyloadComponent(() => import('./onboarding/Onboarding'))();

export const CareerGrowth = LazyloadComponent(() =>
  import('./TalentMarketplace/MyOpportunities')
)();

export const OktaVerify = LazyloadComponent(() => import('./Login/OktaVerify'))();

export const MkpRedirect = LazyloadComponent(() => import('./mkpRedirect'))();

export const CreateOrDuplicateProjectPage = LazyloadComponent(() =>
  import('./Projects/CreateOrDuplicateProjectPage')
)();

export const EditProjectPage = LazyloadComponent(() => import('./Projects/EditProject'))();

export const ViewProjectPage = LazyloadComponent(() => import('./Projects/ViewProject'))();

export const MyProjects = LazyloadComponent(() => import('./TalentMarketplace/MyProjects'))();

export const ManageProjectPage = LazyloadComponent(() => import('./Projects/ManageProject'))();

export const MyMentorshipsPage = LazyloadComponent(() =>
  import('./TalentMarketplace/Mentorship/pages/MyMentorshipPage')
)();

export const MentorshipProfilePage = LazyloadComponent(() =>
  import('./TalentMarketplace/Mentorship/pages/MentorshipProfilePage')
)();

export const LedgerContainer = LazyloadComponent(() => import('./LedgerV2'))();

export const LoginSignupContainer = LazyloadComponent(() =>
  import('./Login/LoginSignupContainer')
)();

export const LoginLayout = LazyloadComponent(() => import('./Login/LoginLayout'))();

export const SignUpLayout = LazyloadComponent(() => import('./Login/SignUpLayout'))();

export const PreRegistrationForm = LazyloadComponent(() => import('./Login/PreRegistrationForm'))();

export const MicroOrgSsoSection = LazyloadComponent(() => import('./Login/MicroOrgSsoSection'))();

export const FeaturedProviderPage = LazyloadComponent(() =>
  import('../components/provider/FeaturedProviderContainer')
)();

export const Sourcing = LazyloadComponent(() => import('./Sourcing/routes/main-page'))();

export const ManageJobVacancySourcing = LazyloadComponent(() =>
  import('./Sourcing/routes/manage-page/job-vacancy')
)();

export const RestrictedAccesssOMPPage = LazyloadComponent(() =>
  import('./TalentMarketplace/RestrictedAccessPage')
)();

export const ManageBlockedUsers = LazyloadComponent(() =>
  import('./ManageBlockedUsers/ManageBlockedUsers')
)();

export const HomeV2 = LazyloadComponent(() => import('./HomeV2'))();

export const CurriculumPlayerMFE = LazyloadComponent(() => import('./CurriculumPlayerMFE'))();
export const OneplayerMFE = LazyloadComponent(() => import('./OnePlayerMFE'))();

export const MyLearningV2 = LazyloadComponent(() => import('./MyLearningV2/MyLearningContainer'))();
export const SearchPeopleV2 = LazyloadComponent(() => import('./SearchPeopleV2/SearchPeople'))();
