export const getTabPath = (tabName, id) => {
  if (tabName === 'Associated Channels' || tabName === 'Channels') {
    return 'channels';
  }

  if (tabName === 'Group Assignments' || tabName === 'Assigned') {
    return 'assignments';
  }

  if (id) {
    return id.toString();
  }

  return tabName
    .replace(/\s/g, '-')
    .toLowerCase()
    .replace(/[&\/\\#,+()$~%.'":*?<>{}]/g, '');
};

export default getTabPath;
