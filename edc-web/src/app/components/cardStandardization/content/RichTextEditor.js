import React from 'react';
import configWrapper from '../hoc/configWrapper';
import PropTypes from 'prop-types';
import Loadable from 'react-loadable';

const RichTextReadOnly = Loadable({
  loader: () => import('../../../../app/components/common/RichTextReadOnly'),
  loading: () => null
});

const RichTextEditor = ({ message, tagType, truncate = false }) => {
  return <RichTextReadOnly text={message} tagType={tagType} truncate={truncate} />;
};

RichTextEditor.propTypes = {
  truncate: PropTypes.bool,
  message: PropTypes.string,
  tagType: PropTypes.string
};
export default configWrapper(RichTextEditor);
