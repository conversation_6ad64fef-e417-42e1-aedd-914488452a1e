import React, { useContext } from 'react';
import { object, bool } from 'prop-types';
import configWrapper from '../hoc/configWrapper';
import RichTextEditor from './RichTextEditor';
import TextWrapper from './TextWrapper';
import CardContext from '../context/CardContext';
import classNames from 'classnames';

const Title = props => {
  const { configData, isStandaloneLayout, listCard = false } = props;
  const CardContextData = useContext(CardContext);
  const { card } = CardContextData;
  const configObject = { configData: true, message: configData.message, isStandaloneLayout };
  const textWrapperConfig = !configData.showRTE ? configObject : null;
  const richTextEditorConfig = configData.showRTE ? configObject : null;

  return (
    <div
      id={`card-title-${card?.id}`}
      className={classNames('card-std-text', {
        'card-std-text-without-thumbnails': configData.withoutThumbnails,
        'html-container': configData.showRTE,
        'height-100': listCard
      })}
    >
      <TextWrapper listCard={listCard} {...textWrapperConfig} />
      <RichTextEditor truncate={!isStandaloneLayout} {...richTextEditorConfig} />
    </div>
  );
};

Title.propTypes = {
  configData: object,
  isStandaloneLayout: bool,
  listCard: bool
};

export default configWrapper(React.memo(Title));
