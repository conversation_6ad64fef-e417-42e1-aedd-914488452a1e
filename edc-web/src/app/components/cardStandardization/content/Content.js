import React, { useContext, useState, useEffect, useMemo, useRef } from 'react';
import { string, bool, object, number } from 'prop-types';
import { translatr } from 'centralized-design-system/src/Translatr';

import Title from './Title';
import ThumbnailWrapper from './ThumbnailWrapper';
import AssignmentDetails from './AssignmentDetails';

import Description from './Description';
import ResourceWrapper from './resource/ResourceWrapper';
import classNames from 'classnames';
import AdditionalMetadataForVILTCard from './AdditionalMetadataForVILTCard';
import AdditionalMetadataForProjectCard from './AdditionalMetadataForProjectCard';
import File from './File';
import InlineFile from './InlineFile';
import VideoProcessingComponent from './VideoProcessingComponent';
import AdditionalMetadataForScormCard from './scorm/AdditionalMetadataForScormCard';
import Poll from './poll/Poll';
import EmbedHtml from './EmbedHtml';

import LiveStreamRedirectMsgOnTile from './videostream/LiveStreamRedirectMsgOnTile';
import VideoStreamEmbed from './videostream/VideoStreamEmbed';
import LiveStreamEmbed from './videostream/LiveStreamEmbed';
import AgoraLiveStreamEmbed from './videostream/AgoraLiveStreamEmbed';
import MultiQuizConsumptionWrapper from './quiz/MultiQuizConsumptionWrapper';
import MultiQuizShowWrapper from './quiz/MultiQuizShowWrapper';
import ResultWrapper from './ResultWrapper';
import './_Content.scss';
import PaidContent from '../private/PaidContent';
import CardContext from '../context/CardContext';
import LD from '../../../../app/containers/LDStore';
import { isRegisterAndPlay, isLmsWorkFlowTrue, isLmsPaidContent } from '@utils/utils';
import LMSContentMsg from './LMSContentMsg';
import OptionalStatus from './OptionalStatus';
import { PATHWAY_AND_JOURNEY_PATHS } from '@utils/constants';
import TranscriptActionButtons from './TranscriptActionButtons';
import ScormPlayerContainer from './scorm/ScormPlayerContainer';
import TroubleOpeningMfe from '../common/TroubleOpeningMfe';

const Content = props => {
  const {
    type,
    configContent,
    cardId,
    filestackUrlExpire,
    currentUserId,
    showContent,
    isText,
    isTextCardHasTitleWithoutImage,
    isResourceCard,
    isShowPricingModal,
    cardUrl,
    openInNewWindow,
    blankAlt,
    skill,
    shouldShowLmsWorkflowMsg = false
  } = props;
  const typeLowerCase = type.toLowerCase();
  const isStandaloneLayout = typeLowerCase === 'standalone';
  const CardContextData = useContext(CardContext);
  const cardDetails = CardContextData.card;

  const linkRef = useRef();

  const {
    curriculumContentData = {},
    csxIltContentData = {},
    isLaunchContentBtnDisplayed
  } = CardContextData;

  const { cardType, cardSubtype, resource } = cardDetails;
  const cardTitle = cardDetails?.title || cardDetails?.cardTitle || cardDetails?.message;

  const titleConfig = configContent.title
    ? {
        message: configContent.title,
        withoutThumbnails: !configContent.thumbnail,
        showRTE: configContent.showRTE && !isStandaloneLayout
      }
    : null;
  const descriptionConfig = configContent.description
    ? {
        message: configContent.description,
        showRTE: configContent.showRTE,
        isTextCardWithHTMLContent: configContent.isTextCardWithHTMLContent,
        isImageAvailableOnTileView:
          typeLowerCase === 'tile' &&
          (configContent.thumbnailDetails?.configData || configContent.fileConfig?.configData),
        showCKEditorDescription: configContent.showCKEditorDescription,
        isTextCardHasTitleWithoutImage,
        openInNewWindow
      }
    : null;

  const [showCourseIframe, setShowCourseIframe] = useState(false);

  const isPathwayOrJourneyPage = useMemo(() => {
    const pathname = window.location.pathname;
    const firstSegment = pathname.split('/')[1] || '';

    return PATHWAY_AND_JOURNEY_PATHS.includes(`/${firstSegment.toLowerCase()}/`);
  }, []);

  const shouldHideDescriptionForVideo =
    cardSubtype === 'video' && resource && resource.embedHtml === null && !!resource.description;

  const ifResourceOrScormMetadataPresent =
    configContent.resourceWrapper || configContent.additionalMetadataForScormCard;

  const isAgoraLiveStreamEnabled = configContent.liveStreamEmbed;
  const LiveStreamComponent = isAgoraLiveStreamEnabled ? LiveStreamEmbed : AgoraLiveStreamEmbed;
  const {
    fileConfig,
    resourceWrapper,
    thumbnailDetails,
    showPromotedText,
    videoEmbed,
    additionalMetadataForScormCard,
    showLiveStreamRedirectMsg,
    videoStreamEmbed,
    liveStreamEmbed,
    showVideoProcessing
  } = configContent;
  if (!!configContent.fileConfig) configContent.fileConfig.cardTitle = configContent.title;
  const isThumbnailPresent =
    fileConfig ||
    resourceWrapper ||
    thumbnailDetails ||
    videoEmbed ||
    additionalMetadataForScormCard ||
    showLiveStreamRedirectMsg ||
    videoStreamEmbed ||
    liveStreamEmbed ||
    showVideoProcessing;
  const classnames = classNames(
    'card-std-content',
    `card-std-content-${typeLowerCase}`,
    {
      'card-std-resource-present': ifResourceOrScormMetadataPresent
    },
    { 'text-without-thumbnail': isText && !isThumbnailPresent }
  );
  const assignmentDetailsClassNames = classNames(`assignment-details ${cardType}`, {
    'with-thumbnail': isThumbnailPresent
  });
  const wrapperClasses = classNames('thumbnail-resource-wrapper', {
    'card-std-special-design-for-standalone':
      ifResourceOrScormMetadataPresent && !configContent.embedHtml,
    'flex-column': LD.isEmbedHtmlPlayInlineEnabled() && configContent.embedHtml
  });
  const showRegisterAndPlay = isRegisterAndPlay(CardContextData.card, isStandaloneLayout);

  const failedContentRegistration = (() => {
    const isMfeRegistrationEnabled =
      curriculumContentData?.isRegistrationForMfeEnabled ||
      csxIltContentData?.isRegistrationForMfeEnabled;

    if (!isMfeRegistrationEnabled) {
      return false;
    }

    return (
      curriculumContentData?.curriculumRegistrationFailed ||
      csxIltContentData?.iltMfeRegistrationFailed
    );
  })();

  const showLmsWorkFlowMsg =
    (shouldShowLmsWorkflowMsg && isLmsWorkFlowTrue(CardContextData.card)) ||
    isLmsPaidContent(cardDetails) ||
    failedContentRegistration ||
    cardDetails?.isMfeOnePlayerEnabled;

  //Show same admin implementation for resource card to member user
  const isPaidCard = !showContent && isStandaloneLayout && !isResourceCard;
  const showDynamicHeight =
    !configContent.resourceWrapper && !configContent.additionalMetadataForScormCard;
  const isScormCardStandaloneLayout =
    configContent.additionalMetadataForScormCard && isStandaloneLayout;
  const hideThumbnailForFileCardInStandaloneLayout =
    isStandaloneLayout &&
    configContent.fileConfig?.configData &&
    configContent.fileConfig?.mimeType !== 'audio';
  const showThumbnailDetails =
    !(type === 'featured' && !configContent.resourceWrapper) &&
    !isPaidCard &&
    !hideThumbnailForFileCardInStandaloneLayout;
  const showFileAfterThumbnail =
    type !== 'featured' && configContent?.fileConfig?.mimeType === 'audio';

  const cardResourceLink =
    resourceWrapper?.resourceDescriptionAndUrl?.resourceLink || resourceWrapper.resourceLink;
  const hasTranscript = fileConfig?.mimeType === 'video' && fileConfig?.transcriptUrl;

  const [showScormModal, setShowScormModal] = useState(false);
  const [scormRegId, setScormRegId] = useState(null);
  const [displayTranscript, setDisplayTranscript] = useState(true);

  const renderThumbnailWrapper = () => {
    return (
      <ThumbnailWrapper
        {...configContent.thumbnailDetails}
        type={typeLowerCase}
        cardId={cardId}
        filestackUrlExpire={filestackUrlExpire}
        currentUserId={currentUserId}
        showPlayIcon={configContent.showPlayIcon}
        scormErrorMessage={configContent.scormErrorMessage}
        cardLanguageErrorMessage={configContent.cardLanguageErrorMessage}
        isShowDynamicHeight={showDynamicHeight}
        isScormCard={thumbnailDetails?.isScormCard}
        showOverlay={['pack', 'journey'].includes(cardType) && !isStandaloneLayout}
        {...configContent.scheduleStreamStartTime}
        setShowCourseIframe={showRegisterAndPlay && setShowCourseIframe}
        showCourseIframe={showRegisterAndPlay && showCourseIframe}
        showRegisterAndPlay={showRegisterAndPlay}
        setScormRegId={setScormRegId}
        setShowScormModal={setShowScormModal}
        blankAlt={blankAlt}
        skill={skill}
      />
    );
  };

  const cardContentId = `card-content-${cardId}`;

  const cardContent = (
    <>
      {/* For isCardDisabled true, the cardUrl will be null */}
      <a href={cardUrl} aria-disabled={CardContextData.isCardDisabled} ref={linkRef}>
        <Title configData={titleConfig} isStandaloneLayout={isStandaloneLayout} />
      </a>

      <OptionalStatus
        className={'position-absolute'}
        configData={typeLowerCase === 'tile' && isPathwayOrJourneyPage}
      />

      <div id={cardContentId} data-is-delegatable-to-on-click={true}>
        {!showFileAfterThumbnail && (
          <File
            {...configContent.fileConfig}
            {...(hasTranscript && {
              displayTranscript,
              onTranscriptToggle: setDisplayTranscript
            })}
          />
        )}
        {!showFileAfterThumbnail && <InlineFile {...configContent.launchInlineConfig} />}
        {showThumbnailDetails && (
          <>
            <div className={wrapperClasses}>
              {showRegisterAndPlay ? (
                showCourseIframe ? (
                  <EmbedHtml {...configContent.embedHtml} />
                ) : (
                  renderThumbnailWrapper()
                )
              ) : (
                <>
                  {renderThumbnailWrapper()}
                  <EmbedHtml {...configContent.embedHtml} />
                </>
              )}
              <ResourceWrapper {...configContent.resourceWrapper} />
              <AdditionalMetadataForScormCard
                {...configContent.additionalMetadataForScormCard}
                setScormRegId={setScormRegId}
                setShowScormModal={setShowScormModal}
              />
            </div>

            {/* Curriculum/onePlayer trouble opening content with open content link */}
            <TroubleOpeningMfe
              configData={
                isStandaloneLayout &&
                (curriculumContentData?.isOpenCurriculumBtnDisplayed ||
                  isLaunchContentBtnDisplayed) &&
                cardResourceLink
              }
            />
            {thumbnailDetails?.isScormCard && showScormModal && (
              <ScormPlayerContainer
                contentTitle={cardTitle}
                contentId={scormRegId}
                showScormModal={showScormModal}
                onClose={() => setShowScormModal(false)}
              />
            )}
          </>
        )}
        {showFileAfterThumbnail && <File {...configContent.fileConfig} />}
        {showFileAfterThumbnail && <InlineFile {...configContent.launchInlineConfig} />}

        <PaidContent
          thumbnailDetails={configContent.thumbnailDetails}
          type={typeLowerCase}
          cardId={cardId}
          filestackUrlExpire={filestackUrlExpire}
          currentUserId={currentUserId}
          showPlayIcon={configContent.showPlayIcon}
          configData={isPaidCard}
          isShowPricingModal={isShowPricingModal}
        />
        {type !== 'featured' && (
          <>
            <LiveStreamRedirectMsgOnTile configData={configContent.showLiveStreamRedirectMsg} />
            <VideoStreamEmbed {...configContent.videoStreamEmbed} />
            <LiveStreamComponent {...configContent.liveStreamEmbed} />
            <VideoProcessingComponent configData={configContent.showVideoProcessing} />
          </>
        )}
        <Description
          configData={
            !(isScormCardStandaloneLayout || shouldHideDescriptionForVideo) && descriptionConfig
          }
        />
        {hasTranscript && (
          <TranscriptActionButtons
            onTranscriptToggle={setDisplayTranscript}
            displayTranscript={displayTranscript}
          />
        )}
        {showLmsWorkFlowMsg && (
          <LMSContentMsg failedContentRegistration={failedContentRegistration} />
        )}
        <AdditionalMetadataForVILTCard {...configContent.additionalMetadataForVILTCard} />
        <Poll {...configContent.pollConfig} />
        <MultiQuizConsumptionWrapper {...configContent.multiQuizConsumptionConfig} />
        <MultiQuizShowWrapper {...configContent.multiQuizShowConfig} />
        <AdditionalMetadataForProjectCard {...configContent.additionalMetadataForProjectCard} />
        <ResultWrapper {...configContent.result} />
      </div>
    </>
  );

  // This function will be attached as keypress event listener for "#my-content-container" element.
  // Every "Enter" keypress event will be handled by this listener function.
  const handleKeypressFn = function(e) {
    if (e.key === 'Enter') {
      // Check if `isDelegatableToOnClick` is "true" to ensure the current element being focused is a "card content".
      e.target.dataset?.isDelegatableToOnClick === 'true' && e.target.click();
    }
  };

  useEffect(() => {
    const cardContentElement = document.getElementById(cardContentId);
    const myContentContainer = document.getElementsByClassName('my-content-container')[0];
    let myContentContainerEventListeners;

    if (typeof getEventListeners !== 'undefined') {
      myContentContainerEventListeners = getEventListeners(myContentContainer); // eslint-disable-line no-undef
    }

    // one-off adding of keypress event listener to "my-content-container" element.
    if (myContentContainer && !myContentContainerEventListeners?.keypress) {
      myContentContainer.addEventListener('keypress', handleKeypressFn);
    }

    if (!cardContentElement) return;
  });

  /**
   * Sets tabindex attribute programmatically after component render.
   *
   * This approach is necessary because applying tabIndex directly on the anchor tag
   * in JSX doesn't persist in the rendered DOM. This could be due to React's synthetic
   * event system or a higher-level component filtering out this attribute during reconciliation.
   *
   * By setting the attribute directly on the DOM element after React has completed rendering,
   * we ensure the title link is removed from the keyboard tab sequence,
   * by preventing unwanted focus on the title when users navigate with the Tab key.
   *
   * @depends linkRef - Reference to the anchor tag DOM element
   * @triggers When titleConfig changes, ensuring the attribute is reapplied if content updates
   */
  useEffect(() => {
    if (linkRef.current) {
      linkRef.current.setAttribute('tabindex', '-1');
    }
  }, [titleConfig]);

  return (
    <div className={`${classnames} ${cardType}-tile-container`}>
      <AssignmentDetails
        configData={typeLowerCase === 'tile' && configContent.assignmentConfigTile}
        classNames={assignmentDetailsClassNames}
      />
      {showPromotedText && (
        <div className="promoted-content-text position-absolute font-size-m s-padding-ends  m-padding-sides">
          {translatr('web.search.main', 'Promoted')}
        </div>
      )}
      {isStandaloneLayout && (cardType === 'quiz' || cardType === 'poll') ? (
        <fieldset className={`${classnames} fieldset-list`}>{cardContent}</fieldset>
      ) : (
        <div className={`${classnames} ${cardType}-tile`}>{cardContent}</div>
      )}
    </div>
  );
};

Content.propTypes = {
  type: string,
  configContent: object,
  cardId: string,
  currentUserId: string,
  filestackUrlExpire: number,
  showContent: bool,
  isText: bool,
  cardType: string,
  isTextCardHasTitleWithoutImage: bool,
  isResourceCard: bool,
  isShowPricingModal: bool,
  cardUrl: string,
  openInNewWindow: bool,
  blankAlt: bool,
  skill: string,
  shouldShowLmsWorkflowMsg: bool
};

export default Content;
