import React, { useContext } from 'react';
import PropTypes from 'prop-types';
import { translatr } from 'centralized-design-system/src/Translatr';
import CardContext from '../context/CardContext';
import {
  isMediaTypeMeterial,
  hasPostWorkActivity,
  hasIncompletePreReq,
  hasIncompletePreWork,
  isContentStatusNotAllowedForInlinePlay,
  hasTranscriptWithValidStatus,
  isPaymentCompleted,
  isLmsPaidContent,
  restrictAccessToLmsPaidContentWithWorkflow
} from '../../../utils/utils';

const LMSContentMsg = ({ failedContentRegistration }) => {
  const cardContextData = useContext(CardContext);
  const { card, isStandalone } = cardContextData;
  const { requiresApprovalsForUser, hasRequiredPostwork } = card?.lmsWorkflows || {};
  const { approvalPending, requiredPostworkPending, contentStatus } = card?.transcriptData ?? {};
  const { mediaType } = card?.contentInlinePlayConfiguration || {};

  const getPostCompletionMsg = () => {
    return isMediaTypeMeterial(mediaType)
      ? translatr('web.smartcard.standalone', 'PostWorkMsgForMaterialCards')
      : translatr('web.smartcard.standalone', 'PostWorkMessage');
  };

  const hasCompletedPayment =
    hasTranscriptWithValidStatus(card) && isPaymentCompleted(contentStatus);
  const hasPrice = isLmsPaidContent(card);
  const showPostWorkMsg = hasPostWorkActivity(card);

  let showMessage = '';

  if (failedContentRegistration) {
    showMessage = translatr('web.smartcard.standalone', 'WeCouldNotRegisterYouToTheContent');
  } else if (hasIncompletePreReq(card)) {
    showMessage = translatr('web.smartcard.standalone', 'PreRequisitesMsg');
  } else if (hasIncompletePreWork(card)) {
    showMessage = translatr('web.smartcard.standalone', 'PreWorkMsg');
  } else if (requiresApprovalsForUser && approvalPending !== false) {
    showMessage = translatr('web.smartcard.standalone', 'ApprovalMessage');
  } else if (requiresApprovalsForUser && isContentStatusNotAllowedForInlinePlay(contentStatus)) {
    showMessage = translatr('web.smartcard.standalone', 'ApprovalMessage');
  } else if (hasPrice && restrictAccessToLmsPaidContentWithWorkflow(card)) {
    showMessage = translatr('web.smartcard.standalone', 'LmsPaidCardWithWorkflowAndNotCompleted');
  } else if (hasPrice && !hasTranscriptWithValidStatus(card)) {
    showMessage = translatr('web.smartcard.standalone', 'PaidContentPaymentRequired');
  } else if (hasPrice && !hasCompletedPayment) {
    showMessage = translatr('web.smartcard.standalone', 'PendingPaymentOrApproval');
  } else if ((hasRequiredPostwork && requiredPostworkPending !== false) || showPostWorkMsg) {
    showMessage = getPostCompletionMsg();
  }

  return (
    <>
      {isStandalone && showMessage?.length > 0 && (
        <div className="lms-msg-container justflex flex-column mb-16 position-relative font-size-xl">
          {showMessage}
        </div>
      )}
    </>
  );
};

LMSContentMsg.propTypes = {
  failedContentRegistration: PropTypes.bool
};

export default LMSContentMsg;
