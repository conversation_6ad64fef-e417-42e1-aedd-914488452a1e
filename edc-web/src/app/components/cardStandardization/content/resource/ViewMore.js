import React, { useContext } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import configWrapper from '../../hoc/configWrapper';
import { translatr } from 'centralized-design-system/src/Translatr';

import CardContext from '../../context/CardContext';
import { open_v2 as openSnackBar } from '../../../../../app/actions/snackBarActions';
import getPriceObject from '../../utils/getPriceObject';
import showInlineContent, { inlineLinkMediaType } from '../../utils/content/showInlineContent';
import { FILE_MEDIA_TYPE } from '../../common/constants';
import { getPromotionalParams } from '@components/cardStandardization/utils/marketPlaceServices';
import isLmsProviderEnabledInstance from '@utils/isLmsProviderEnabledInstance';
import {
  hasPostWorkActivity,
  isMediaTypeMeterial,
  hasPreActivity,
  isLmsPaidContent,
  isPaymentCompleted,
  isLmsWorkFlowTrue,
  isContentCompleted
} from '../../../../utils/utils';

/**
 * Determines if paid LMS content requires LMS redirection
 *
 * Business Logic:
 * - Non-paid content: Always allow direct access
 * - Paid content with workflow: Block access until content is completed
 * - Paid content without workflow: Block access until payment is completed
 *
 * @param {Object} card - The card object containing content information
 * @param {string|null} contentStatus - Content status from transcript data
 * @returns {boolean} True if content should redirect to LMS, false for direct access
 */

const shouldRedirectToLmsForPaidContent = (card, contentStatus) => {
  if (!isLmsPaidContent(card)) {
    return false;
  }

  if (isLmsWorkFlowTrue(card)) {
    return !isContentCompleted(card);
  }
  return !isPaymentCompleted(contentStatus);
};

const ViewMore = props => {
  const { configData, launchInlineConfig = {}, hasUserLoggedInViaMicroSite } = props;

  const cardContext = useContext(CardContext);
  const {
    card,
    updateCard,
    isStandalone,
    currentUserCountryCode,
    cardParams = {},
    isCardFromMarketPlace
  } = cardContext;
  const { deepLinkingURL } = getPromotionalParams(cardParams);
  const { launchInline, contentInlinePlayConfiguration, lmsWorkflows, transcriptData = {} } = card;
  const { launchUrl, mediaType } = launchInlineConfig;
  const LINK_REL_ATTRIBUTE = 'noopener,noreferrer';
  const cardTitle = card?.cardTitle || card?.cardDescription;

  const shouldOpenInLMSContent =
    (lmsWorkflows?.hasRequiredPostwork && transcriptData?.requiredPostworkPending !== false) ||
    hasPostWorkActivity(card) ||
    hasPreActivity(card) ||
    shouldRedirectToLmsForPaidContent(card, transcriptData?.contentStatus);

  const handleUrlClicked = (cardData, callUpdateCard, redirectURL, e) => {
    e.preventDefault();

    if (
      isLmsProviderEnabledInstance() &&
      hasUserLoggedInViaMicroSite &&
      card.isContentFromInternalLms &&
      card.resource?.url
    ) {
      return openInlineLaunchUrl(card.resource.url);
    } else if (
      (showInlineContent(card) && FILE_MEDIA_TYPE.includes(mediaType)) ||
      (inlineLinkMediaType(card) && !shouldOpenInLMSContent)
    ) {
      // Download the file media type on clicking open content btn
      // For mediaType link we need to open the `launchUrl` link in external window
      return openInlineLaunchUrl(launchUrl);
    } else if (
      'launchInline' in card &&
      !launchInline &&
      contentInlinePlayConfiguration?.launchUrl
    ) {
      // if launchInline key is present in card and its value is false then we need to redirect to launchinline launchurl
      // And if launchUrl is not present we should follow normal smartcard flow
      return openInlineLaunchUrl(contentInlinePlayConfiguration?.launchUrl);
    } else if (isMediaTypeMeterial(mediaType) && shouldOpenInLMSContent) {
      return openInlineLaunchUrl(redirectURL);
    } else if (
      isMediaTypeMeterial(mediaType) &&
      !shouldOpenInLMSContent &&
      !showInlineContent(card)
    ) {
      //If there are no pre/post conditions for material types of cards, then download the image.
      return openInlineLaunchUrl(launchUrl);
    }

    const priceObject = getPriceObject(card, currentUserCountryCode);
    if (isStandalone && isCardFromMarketPlace && card && card.isPaid && priceObject.priceData) {
      if (deepLinkingURL) {
        window.open(deepLinkingURL, '_blank', LINK_REL_ATTRIBUTE);
      } else {
        props.dispatch(
          openSnackBar(translatr('web.common.main', 'SomethingWentWrongPleaseTryAgain'), 'error')
        );
      }
    } else {
      const paymentEnabled = card.paymentEnabled && card.isPaid && card.prices?.length > 0;
      if (paymentEnabled && !card.paidByUser) {
        props.dispatch(
          openSnackBar(translatr('web.common.main', 'PleaseMakePaymentToAccessTheCourse'), 'error')
        );
      } else {
        window.open(redirectURL, '_blank', LINK_REL_ATTRIBUTE);
        cardData.markAsCompleteEnableForLink = true;
        callUpdateCard(cardData);
      }
    }
  };

  const openInlineLaunchUrl = url => {
    url && window.open(url, '_blank', LINK_REL_ATTRIBUTE);
  };

  return (
    <button
      id={`view-more-${card?.id}`}
      aria-label={translatr('web.smartcard.standalone', 'ClickingThisLinkWillOpenContentInNewTab', {
        cardTitle
      })}
      className="ed-link"
      onClick={handleUrlClicked.bind(this, card, updateCard, configData)}
    >
      <i className="icon-external-link no-margin" />
      {translatr('web.common.main', 'OpenContent')}
    </button>
  );
};

ViewMore.propTypes = {
  configData: PropTypes.string,
  launchInlineConfig: PropTypes.object,
  hasUserLoggedInViaMicroSite: PropTypes.bool
};

export default connect(({ currentUser }) => ({
  hasUserLoggedInViaMicroSite: currentUser.get('isUserLoginViaMicrosite')
}))(configWrapper(ViewMore));
