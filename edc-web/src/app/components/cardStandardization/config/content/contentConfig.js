import {
  getTitle,
  getDescription,
  getResourceDetails,
  showRTE,
  isTextCardWithHTMLContent,
  getViltAdditionalMetadata,
  getProjectAdditionalMetadata,
  getFileDetails,
  getLaunchInlineDetails,
  showPlayIcon,
  fetchVideoStreamDetails,
  fetchLiveStreamDetails,
  showVideoProcessing,
  getScormAdditionalMetadata,
  checkIfScormProcssing,
  checkIfLanguageError,
  getPollConfig,
  fetchEmbedHtmlDetails,
  fetchThumbnailDetails,
  fetchScheduledStreamDetails,
  shouldShowLiveStreamRedirectMsg,
  getMultiQuizConsumptionConfig,
  getMultiQuizShowConfig,
  showCKEditorDescription
} from '../../utils/utilsIndex';
import showInlineContent from '../../utils/content/showInlineContent';
import { isAgoraLiveStream } from '@utils/smartCardUtils';

const STANDALONE = 'Standalone';

const contentConfig = (
  layout,
  card,
  orgConfigs,
  currentUserDetails,
  cardType,
  isPathwayOrJourneyAssigned,
  isCurateTab,
  assignmentDetails,
  isPromotedCard
) => {
  const { result } = card;
  const showInline = showInlineContent(card);
  const additionalMetadataForScormCard = getScormAdditionalMetadata(layout, card, orgConfigs);
  const resourceContent = getResourceDetails(
    card,
    layout,
    cardType,
    isCurateTab,
    showInline,
    orgConfigs
  );
  const thumbnailDetails = fetchThumbnailDetails(
    card,
    currentUserDetails?.currentUserId,
    cardType,
    layout,
    showInline,
    true
  );
  const showRTEforTextCard = showRTE(card, cardType, layout);
  const ckeditorDescription = showCKEditorDescription(layout, cardType);
  const textCardWithHTMLContent = !!showRTEforTextCard && isTextCardWithHTMLContent(card, layout);
  const isPathwayOrJourneyStandalonePage =
    layout === STANDALONE && ['pathway', 'journey'].includes(cardType);
  const isHTMLContent =
    ckeditorDescription || textCardWithHTMLContent || isPathwayOrJourneyStandalonePage;
  const fileDetails = getFileDetails(
    card,
    orgConfigs,
    currentUserDetails?.currentUserId,
    layout,
    cardType
  );
  const launchInlineDetails = getLaunchInlineDetails(card, layout);

  return {
    title:
      (layout !== STANDALONE ||
        isCurateTab ||
        (layout === STANDALONE && isAgoraLiveStream(card))) &&
      getTitle(layout, card, cardType, thumbnailDetails?.configData),
    thumbnailDetails,
    showLiveStreamRedirectMsg: shouldShowLiveStreamRedirectMsg(cardType, layout),
    showPlayIcon: showPlayIcon(cardType, card, layout),
    videoStreamEmbed: fetchVideoStreamDetails(
      card,
      cardType,
      orgConfigs,
      currentUserDetails?.currentUserId,
      layout,
      isCurateTab
    ),
    scheduleStreamStartTime: fetchScheduledStreamDetails(card, cardType, layout),
    embedHtml: fetchEmbedHtmlDetails(card, cardType, layout, showInline),
    liveStreamEmbed: fetchLiveStreamDetails(card, cardType, layout, orgConfigs),
    description: getDescription(layout, card, cardType, resourceContent, isHTMLContent, showInline),
    resourceWrapper: resourceContent,
    showRTE: showRTEforTextCard,
    isTextCardWithHTMLContent: textCardWithHTMLContent,
    showVideoProcessing: showVideoProcessing(card, cardType, layout),
    additionalMetadataForVILTCard: getViltAdditionalMetadata(
      layout,
      card,
      cardType,
      currentUserDetails
    ),
    additionalMetadataForProjectCard: getProjectAdditionalMetadata(
      layout,
      card,
      cardType,
      currentUserDetails,
      isPathwayOrJourneyAssigned,
      isCurateTab
    ),
    additionalMetadataForScormCard,
    scormErrorMessage: checkIfScormProcssing(card),
    cardLanguageErrorMessage: checkIfLanguageError(card),
    fileConfig: fileDetails,
    launchInlineConfig: launchInlineDetails,
    pollConfig: getPollConfig(card, layout, currentUserDetails?.currentUserId),
    multiQuizConsumptionConfig: getMultiQuizConsumptionConfig(
      card,
      layout,
      orgConfigs,
      currentUserDetails,
      cardType
    ),
    multiQuizShowConfig: getMultiQuizShowConfig(layout, orgConfigs, cardType),
    personalizedContent: card.explanation?.label,
    assignmentConfigTile:
      ['Tile', 'BigCard'].includes(layout) &&
      assignmentDetails.isAssignmentPage &&
      assignmentDetails.assignedBy &&
      assignmentDetails,
    showCKEditorDescription: ckeditorDescription,
    result: {
      configData: layout === STANDALONE && !!result,
      ...result
    },
    showPromotedText: isPromotedCard && layout === 'Tile'
  };
};

export default contentConfig;
