import React from 'react';
import cn from 'classnames';
import moment from 'moment';
import { translatr } from 'centralized-design-system/src/Translatr';
import DatePickerV2 from 'centralized-design-system/src/DatePickerV2';
import "./DateField.scss";

interface DateFieldProps {
  id?: string,
  label: string,
  required?: boolean,
  error?: string,
  disabled?: boolean,
  date: Date,
  maxDate?: Date,
  minDate?: Date,
  onChange(date: Date): void
}

const MIN_DATE = moment('1950-01-01').toDate();
const MAX_DATE = moment('9999-12-31').toDate();

const DatePickerField: React.FC<DateFieldProps> = ({ id, label, required = false, error, disabled, date, maxDate = MAX_DATE, minDate = MIN_DATE, onChange }) => {
  return (
    <div id={id} className='date-picker-field_container' tabIndex={-1}>
      <label className="ed-input-title">
        {label}
        <span className={`${required ? 'asterisk' : 'optional-text'}`}>
          {required ? "*" : translatr('cds.common.main', 'Optional')}
        </span>
      </label>
      <DatePickerV2
        startDate={date}
        placeHolder={"MM/YYYY"}
        onChange={onChange}
        maxDate={maxDate}
        minDate={minDate}
        className={cn('date-picker-field_date-picker', { errors: Boolean(error), disabled: disabled })}
        showYearDropdown
        onClear={(required && date) ? () => onChange(undefined) : undefined}
        yearDropdownItemNumber={3}
      />
      {required && <span className="date-picker-field_input-error-container input-error">{error}</span>}
    </div>
  );
}

export default DatePickerField;
