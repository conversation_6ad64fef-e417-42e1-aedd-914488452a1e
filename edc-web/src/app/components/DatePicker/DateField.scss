@import '~centralized-design-system/src/Styles/variables';

.date-picker-field_container {
  .date-picker-field_date-picker {
    width: rem-calc(225);

    &.disabled {
      //This style is used to simulate a disabled state for the DatePickerV2, a feature that is not supported through props.
      pointer-events: none;
      filter: opacity(0.5);
    }

    &.errors {
      .react-datepicker-wrapper {
        border-color: var(--ed-negative-2);
      }
    }

    .react-datepicker-wrapper {
      width: 100%;
    }
  }

  .date-picker-field_input-error-container {
    display: inline-block;
  }

  //This style overwrite the CDS date picker that use react-datepicker, this is done
  .react-datepicker {
    .react-datepicker__year-read-view .react-datepicker__year-read-view--down-arrow {
      top: 6px;

      &:hover {
        border-color: var(--ed-gray-5);
      }
    }

    .react-datepicker__navigation--years {
      margin: 0;
    }

    .react-datepicker__navigation--years-upcoming,
    .react-datepicker__navigation--years-previous {
      border: solid black;
      border-width: 0.125rem 0.125rem 0 0;
      display: inline-block;
      padding: 3px;
      width: 0.5rem;
      height: 0.5rem;

      &:hover {
        border-color: black;
      }
    }

    .react-datepicker__navigation--years-upcoming {
      top: 0;
      transform: rotate(-45deg);
    }

    .react-datepicker__navigation--years-previous {
      top: -2px;
      transform: rotate(135deg);
    }
  }
}

.date-picker-field_date-month-year-container {
  margin-bottom: var(--ed-spacing-2xs);

  .date-picker-field_date-month-year-container-wrapper {
    display: flex;
    gap: var(--ed-spacing-2xs);

    select.ed-select {
      box-shadow: var(--ed-input-shadow);
      min-width: rem-calc(136);

      &:focus {
        outline: none;
      }
    }

    &.hasError {
      select.ed-select {
        border-color: var(--ed-negative-2);
      }
    }
  }

  .date-picker-field_input-error-container {
    display: inline-block;
    height: rem-calc(18);
  }
}
