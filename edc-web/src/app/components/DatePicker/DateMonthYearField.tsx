import React from 'react';
import { translatr } from 'centralized-design-system/src/Translatr';
import "./DateField.scss";
import cn from 'classnames';

interface DateFieldProps {
  id?: string,
  label: string,
  required?: boolean,
  month: number,
  monthOptions: Array<any>,
  onMonthChange(newMonth: number): void
  year: string,
  yearOptions: Array<any>
  onYearChange(newYear: string): void,
  error?: string,
}

const DateMonthYearField: React.FC<DateFieldProps> = ({ id, label, required = false, month, monthOptions, onMonthChange, year, yearOptions, onYearChange, error }) => {
  return (
    <div className='date-picker-field_date-month-year-container'>
      <label className="ed-input-title">
        {label}
        <span className="asterisk">
          {required ? "*" : translatr('cds.common.main', 'Optional')}
        </span>
      </label>
      <div className={cn("date-picker-field_date-month-year-container-wrapper", { "hasError": error })}>
        <div>
          <select
            id={id}
            className="ed-select"
            value={year}
            onChange={(e: any) => onYearChange(e.target.value)}
          >
            {yearOptions.map(year => {
              return (
                <option key={year.id} value={year.value}>
                  {year.value}
                </option>
              );
            })}
          </select>
        </div>
        <div>
          <select
            className="ed-select"
            value={month}
            onChange={(e: any) => onMonthChange(e.target.value)}
          >
            {monthOptions.map(month => {
              return (
                <option key={month.id} value={month.id}>
                  {month.value}
                </option>
              );
            })}
          </select>
        </div>
      </div>
      <span className="date-picker-field_input-error-container input-error">
        {error}
      </span>
    </div>
  );
}

export default DateMonthYearField;
