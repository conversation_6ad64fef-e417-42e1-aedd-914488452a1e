import React, { Component } from 'react';
import classNames from 'classnames';

import { translatr } from 'centralized-design-system/src/Translatr';
import { tr } from 'edc-web-sdk/helpers/translations';
import SearchIcon from 'centralized-design-system/src/MUIComponents/icons/Search';
import { DatePicker } from './DatePicker';
import { getUserPerformance } from '../../api/performance';
import FiltersIcon from 'centralized-design-system/src/MUIComponents/icons/Filter';
import BackIcon from 'centralized-design-system/src/MUIComponents/icons/BackIcon';
import EmptyPage from 'centralized-design-system/src/MUIComponents/icons/EmptyPage';
import Spinner from '../common/spinner';
import { NavBar } from './NavBar';
import { getItems } from 'edc-web-sdk/requests/groups.v2';
import {
  getDrilldown,
  linkTo,
  Keys,
  updateData,
  convertDateString,
  decodeHtml
} from './utils/tableUtil';
import { Permissions } from '../../utils/checkPermissions';
import './Performance.scss';

const regEx = /\/teams\/.+\/analytics\/assignment/g;
export const getFromDate = () => {
  const today = new Date();
  if (window.sessionStorage.getItem('analytics-from-date')) {
    return window.sessionStorage.getItem('analytics-from-date');
  } else {
    let numDays = 7;

    // Special case where we need it to be 6 months
    // Used on TeamAssignmentAnalyticsContainer.jsx
    if (regEx.test(document.location.pathname) === true) {
      numDays = 180;
    }
    let d = new Date(today);
    d.setHours(0, 0, 0, 0);
    d.setDate(d.getDate() - numDays);
    return d.toLocalISOString();
  }
};

export const getToDate = () => {
  const today = new Date();
  if (window.sessionStorage.getItem('analytics-to-date')) {
    return window.sessionStorage.getItem('analytics-to-date');
  } else {
    let d = new Date(today);
    d.setHours(23, 59, 59, 999);
    return d.toLocalISOString();
  }
};

class Performance extends Component {
  constructor(props, context) {
    super(props, context);

    this.allowSubadminDrilldown =
      window.__ED__.organization?.configs?.filter(a => a.name === 'allow_subadmin_drilldown')[0]
        ?.value || false;

    // if org admin, can always see drilldown
    this.isTrueAdmin =
      window.__ED__.isOrgAdmin && window.__ED__.isAdmin && Permissions.has('ADMIN_ONLY');
    if (this.isTrueAdmin) {
      this.allowSubadminDrilldown = true;
    }

    const fromDate = getFromDate();
    const toDate = getToDate();
    this.columns = [];
    this.filters = [];
    this.navLabel = '';

    this.state = {
      cardTypeFilterDroppedDown: false,
      headers: [],
      data: [],
      enableSearch: false,
      filtersOpen: false,
      fromDate, // a month ago
      isLoading: true,
      lastUpdated: '',
      modalOpen: false,
      isShowSuggestions: false,
      page: 1,
      sortDir: 'desc',
      searchStr: '',
      toDate,
      selectedFilters: new Set(),
      selectedCardTypeFilters: new Set(),
      total: 0,
      teams: null,
      teamSelected: 'all'
    };

    this.getFilteredData = this.getFilteredData.bind(this);
    this.filtersOpenRef = null;
  }
  async componentDidMount() {
    // If user is an Admin or OrgAdmin then they can see all teams
    // If we had already preloaded the state.teams, do not attempt to get a list of teams.

    if (!this.isTrueAdmin && !Permissions.has('MANAGE_ANALYTICS') && !this.state.teams) {
      await this.getSubAdmin();
    }
    await this.getData();
  }

  getSubAdmin() {
    return new Promise(resolve => {
      getItems({ 'role[]': ['sub_admin', 'admin'], fields: 'id, name' })
        .then(teams => {
          if (teams?.length > 0) {
            this.setState({ teams: teams }, resolve);
          } else {
            resolve();
          }
        })
        .catch(err => {
          console.error(`Error in Performance.getSubAdmin.func : ${err}`);
          resolve();
        });
    });
  }

  renderLoading() {
    return (
      <div className="spinner-container">
        <Spinner />
      </div>
    );
  }

  renderEmptyPage(error = '') {
    const theMessage =
      error === '' ? translatr('web.common.main', 'NoRecordsForTimePeriod') : tr(error);
    return (
      <div className="analytics-empty-page">
        <EmptyPage />
        <p>{theMessage}</p>
      </div>
    );
  }

  getFilteredData() {
    // @STUB
  }

  handleTeamSelected = e => {
    this.setState({ teamSelected: e.target.value }, this.getData);
  };

  renderTeamsDropdown() {
    let output = null;

    if (this.state.teams) {
      output = (
        <select
          className="team-select"
          defaultValue={this.state.teamSelected}
          onChange={this.handleTeamSelected}
        >
          <option value="all">{translatr('web.common.main', 'AllTeams')}</option>
          {this.state.teams.map(t => {
            return (
              <option value={t.id} key={t.id}>
                {t.name}
              </option>
            );
          })}
        </select>
      );
    }

    return output;
  }

  renderNav(withNavBar = true, singlePage = false) {
    const {
      filtersOpen,
      selectedFilters,
      cardTypeFilterDroppedDown,
      selectedCardTypeFilters
    } = this.state;

    let downloadTotal = this.state.total || this.state.totalModalData || null;
    if (downloadTotal) {
      if (downloadTotal > 1000) {
        downloadTotal = '(1000)';
      } else {
        downloadTotal = `(${downloadTotal})`;
      }
    }

    return (
      <>
        {withNavBar && <NavBar active={this.name} />}
        {this.state.modalOpen ? (
          <nav className="table-menus back">
            <button
              className="make-center backIcon pointer"
              onClick={() => this.setState({ modalOpen: false })}
            >
              <BackIcon color="#595979" /> {this.state.modalTitle}
            </button>
          </nav>
        ) : (
          <>
            <nav className="table-menus">
              {this.name === 'assignment' && (
                <ul className="subnav">
                  <li>
                    <button
                      className={this.state.tab === 'individual' && 'active'}
                      href="javascript:void(0)"
                      onClick={() => this.getData('individual')}
                    >
                      {translatr('web.common.main', 'Individual')}
                    </button>
                  </li>
                  <li>
                    <button
                      className={this.state.tab === 'group' && 'active'}
                      href="javascript:void(0)"
                      onClick={() => this.getData('group')}
                    >
                      {translatr('web.common.main', 'Group')}
                    </button>
                  </li>
                </ul>
              )}
              {this.name === 'group' && (
                <ul className="subnav">
                  <li>
                    <button
                      className={this.state.tab === 'group' && 'active'}
                      href="javascript:void(0)"
                      onClick={() => this.updateHeaders('group')}
                    >
                      {translatr('web.common.main', 'Summary')}
                    </button>
                  </li>
                  <li>
                    <button
                      className={this.state.tab === 'individual' && 'active'}
                      href="javascript:void(0)"
                      onClick={() => this.updateHeaders('individual')}
                    >
                      {translatr('web.common.main', 'UserReport')}
                    </button>
                  </li>
                </ul>
              )}
              {this.name === 'channel' && (
                <ul className="subnav">
                  <li>
                    <button
                      className={this.state.tab === 'overview' && 'active'}
                      href="javascript:void(0)"
                      onClick={() => this.updateHeaders('overview')}
                    >
                      {translatr('web.common.main', 'Overview')}
                    </button>
                  </li>
                  <li>
                    <button
                      className={this.state.tab === 'content_report' && 'active'}
                      href="javascript:void(0)"
                      onClick={() => this.updateHeaders('content_report')}
                    >
                      {translatr('web.common.main', 'ContentReport')}
                    </button>
                  </li>
                </ul>
              )}
              <div className="analytics-label">{this.navLabel}</div>
              <div className="analytics-right">
                {!singlePage && this.renderTeamsDropdown()}
                {this.showFilters() && (
                  <div id="filters-container" className="position-relative s-margin-sides">
                    <button
                      id="filters-button"
                      className={classNames(
                        'align-items-center',
                        'flex-space-between',
                        'bg-color-white',
                        'navigation-action',
                        'nowrap'
                      )}
                      onClick={e => this.openFiltersDropdown(e)}
                    >
                      <FiltersIcon className={classNames('height-100')} />{' '}
                      {!selectedFilters.size
                        ? translatr('web.common.main', 'AddFilter')
                        : selectedFilters.size === 1
                        ? translatr('web.common.main', 'OneFilter')
                        : translatr('web.common.main', 'FiltersizeFilters', {
                            filterSize: selectedFilters.size
                          })}
                    </button>
                    <div
                      ref={instance => (this.filtersOpenRef = instance)}
                      id="filters-dropdown"
                      className={classNames(
                        'dropdown-box-shadow',
                        'z-index-max',
                        's-margin-ends',
                        's-padding',
                        'position-absolute',
                        'bg-color-white',
                        'xs-border-radius',
                        'nowrap',
                        {
                          hidden: !filtersOpen
                        }
                      )}
                    >
                      {/* Get tab's list of filterables, list here... */}
                      <ul className="strip-default-styles">
                        {this.filters.map((filter, index) => (
                          <li
                            key={`${filter}${index}`}
                            className="flex place-items-center xs-padding"
                          >
                            <input
                              className="xs-margin-right cursor-pointer"
                              type="checkbox"
                              id={`filter-${filter}`}
                              checked={this.state.selectedFilters.has(filter)}
                              onChange={() => {
                                let filtersCopy = new Set(this.state.selectedFilters);
                                if (this.state.selectedFilters.has(filter)) {
                                  filtersCopy.delete(filter);
                                } else {
                                  filtersCopy.add(filter);
                                }
                                this.setState({ selectedFilters: filtersCopy }, () => {
                                  // @TODO call search with query params
                                });
                              }}
                            />
                            <label
                              htmlFor={`filter-${filter}`}
                              className="no-margin cursor-pointer"
                            >
                              {/* use Translation mapping strategy for friendly filter name */}
                              {this.translations.filters[filter]}
                            </label>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                )}

                <DatePicker
                  onDateChange={this.onDateChange.bind(this)}
                  fromDate={this.state.fromDate}
                  toDate={this.state.toDate}
                />
                {this.showSearch() && (
                  <div className="search">
                    <input
                      type="text"
                      value={this.state.searchStr}
                      placeholder={tr(this.getSearchLabel(this.name))}
                      onChange={this.getSearchStr}
                      onFocus={() => this.setState({ enableSearch: true })}
                      maxLength={this.name === 'user' ? '50' : 'null'}
                    />
                    <SearchIcon className="search-icon" onClick={() => this.getSearchData()} />
                  </div>
                )}
                {this.name !== 'overview' && (
                  <button
                    className="download-btn"
                    disabled={!downloadTotal}
                    onClick={this.downloadCSV}
                  >
                    <i></i>
                    {translatr('web.common.main', 'Download')} {downloadTotal}
                  </button>
                )}
              </div>
            </nav>

            {/* showFilters() must be enabled for `selectedFilters` to be populated, but I want to be safe */}
            {this.showFilters() && selectedFilters.size > 0 && (
              <div id="filters-subnav" className="flex m-padding">
                {[...selectedFilters].map(selectedFilter => (
                  <React.Fragment key={`filter-options-${selectedFilter}`}>
                    {selectedFilter === 'author' ? (
                      <div id="filter-subnav-author">
                        <input
                          className="xs-border-radius bg-color-white"
                          style={{ border: '1px solid' }} // @TODO don't inline
                          type="text"
                          placeholder={translatr('web.common.main', 'AuthorName')}
                        />
                      </div>
                    ) : selectedFilter === 'cardType' ? (
                      <div id="filter-subnav-cardType" className="position-relative">
                        <button
                          className="primary-button"
                          onClick={() => {
                            this.setState({
                              cardTypeFilterDroppedDown: !cardTypeFilterDroppedDown
                            });
                          }}
                        >
                          {translatr('web.common.main', 'ContentType')}:{' '}
                          {selectedCardTypeFilters.size
                            ? [...selectedCardTypeFilters].join(', ')
                            : 'All'}
                          &nbsp;&nbsp;∨
                        </button>
                        <div
                          className={classNames(
                            'z-index-max',
                            's-margin-ends',
                            's-padding',
                            'position-absolute',
                            'bg-color-white',
                            'xs-border-radius',
                            'dropdown-box-shadow',
                            'nowrap',
                            { hidden: !cardTypeFilterDroppedDown }
                          )}
                        >
                          <ul className="strip-default-styles">
                            <li className="flex place-items-center xs-padding">
                              <input
                                className="xs-margin-right cursor-pointer"
                                type="checkbox"
                                id="filterable-card-type-all"
                                checked={!Boolean(selectedCardTypeFilters.size)}
                                onChange={() => {
                                  this.setState({ selectedCardTypeFilters: new Set() });
                                }}
                              />
                              <label
                                htmlFor="filterable-card-type-all"
                                className="no-margin cursor-pointer"
                              >
                                {translatr('web.common.main', 'All')}
                              </label>
                            </li>
                            {this.filterableCardTypes.map((cardType, cardTypeIndex) => (
                              <li
                                key={`${cardType}${cardTypeIndex}`}
                                className="flex place-items-center xs-padding"
                              >
                                <input
                                  className="xs-margin-right cursor-pointer"
                                  type="checkbox"
                                  id={`filterable-card-type-${cardType}`}
                                  checked={this.state.selectedCardTypeFilters.has(cardType)}
                                  onChange={() => {
                                    let selectedCardTypeFiltersCopy = new Set(
                                      this.state.selectedCardTypeFilters
                                    );
                                    if (this.state.selectedCardTypeFilters.has(cardType)) {
                                      selectedCardTypeFiltersCopy.delete(cardType);
                                    } else {
                                      selectedCardTypeFiltersCopy.add(cardType);
                                    }
                                    this.setState(
                                      {
                                        selectedCardTypeFilters: selectedCardTypeFiltersCopy
                                      },
                                      () => {
                                        // @TODO call search with query params
                                      }
                                    );
                                  }}
                                />
                                <label
                                  htmlFor={`filterable-card-type-${cardType}`}
                                  className="no-margin cursor-pointer"
                                >
                                  {cardType}
                                </label>
                              </li>
                            ))}
                          </ul>
                          <div>
                            <button
                              className="ghost-button"
                              onClick={() => this.toggleCardTypeFilterDropdown()}
                            >
                              {translatr('web.common.main', 'Cancel')}
                            </button>
                            <button
                              className="primary-button m-margin-left"
                              onClick={() => {
                                this.getFilteredData();
                              }}
                            >
                              {translatr('web.common.main', 'Apply')}
                            </button>
                          </div>
                        </div>
                      </div>
                    ) : null}
                  </React.Fragment>
                ))}
              </div>
            )}
          </>
        )}
      </>
    );
  }

  renderTableFooter() {
    if (this.state.isLoading) {
      return null;
    }

    let label;
    if (this.state.modalOpen && this.state.totalModalData) {
      label =
        this.state.totalModalData > 1000
          ? translatr('web.common.main', 'Showing1000OfTotalcount', {
              totalCount: this.state.totalModalData
            })
          : translatr('web.common.main', 'ShowingTotalcountOfTotalcount', {
              totalCount: this.state.totalModalData
            });
    } else if (this.state.total) {
      label =
        this.state.total > 1000
          ? translatr('web.common.main', 'Showing1000OfTotalcount', {
              totalCount: this.state.total
            })
          : translatr('web.common.main', 'ShowingTotalcountOfTotalcount', {
              totalCount: this.state.total
            });
    }

    const lastUpdatedAt = convertDateString(this.state.lastUpdated);

    return label ? (
      <nav className="table-footer">
        <label>{label}</label>
        <label>
          {translatr('web.common.main', 'AllTimesInUtc')} |{' '}
          {translatr('web.common.main', 'LastDataRefreshTime')} {lastUpdatedAt}
        </label>
      </nav>
    ) : null;
  }

  formatDate(newDate) {
    return new Date(newDate).toLocalISOString();
  }

  onSort(key) {
    this.setState(
      {
        sortKey: key,
        sortDir: this.state.sortDir === 'asc' ? 'desc' : 'asc',
        page: 1
      },
      this.sortData
    );
  }

  onModalSort(key) {
    this.setState(
      {
        modalSortKey: key,
        modalSortDir: this.state.modalSortDir === 'asc' ? 'desc' : 'asc',
        page: 1
      },
      this.modalSortData
    );
  }

  modalSortData() {
    let sd = this.state.modalSortDir;
    let sb = this.state.modalSortKey;
    let [gt, lt] = sd === 'desc' ? [-1, 1] : [1, -1];
    let keyType = Keys[sb]?.type;
    if (!keyType) {
      if (sb.indexOf('time') > -1) {
        keyType = 'date';
      } else if (sb.indexOf('date') > -1) {
        keyType = 'date';
      } else {
        keyType = 'string';
      }
    }
    let dataSortedBy = this.state.modalData.sort((a, b) => {
      let val1 = a[sb];
      let val2 = b[sb];
      if (parseInt(val1) === val1 || parseInt(val2) === val2) {
        keyType = 'int';
      }
      switch (keyType) {
        case 'string':
          if (sb === 'card_source_name') {
            val1 = a['author'] || a['card_author_name'] || val1;
            val2 = b['author'] || b['card_author_name'] || val2;
          }
          val1 = val1 || '';
          val2 = val2 || '';
          val1 = val1?.trim?.();
          val2 = val2?.trim?.();
          return sd === 'desc'
            ? val2.localeCompare(val1, undefined, { caseFirst: 'upper' })
            : val1.localeCompare(val2, undefined, { caseFirst: 'upper' });
        case 'int':
          // numbers can come back as string
          val1 = parseInt(a[sb]) || 0;
          val2 = parseInt(b[sb]) || 0;
          break;
        case 'date':
          // date case
          val1 = a[sb] ? new Date(a[sb]) : new Date(0);
          val2 = b[sb] ? new Date(b[sb]) : new Date(0);
          break;
        default:
        // If new type or not defined sort by default
      }

      return val1 > val2 ? gt : lt;
    });

    this.setState({
      modalData: dataSortedBy
    });
  }

  sortData() {
    let sd = this.state.sortDir;
    let sb = this.state.sortKey;
    let [gt, lt] = sd === 'desc' ? [-1, 1] : [1, -1];
    let dataSortedBy = this.state.data.sort((a, b) => {
      let val1 = a[sb];
      let val2 = b[sb];
      switch (Keys[sb]?.type) {
        case 'string':
          if (sb === 'author' || sb === 'card_author_name') {
            val1 = val1 || a['card_source_name'];
            val2 = val2 || b['card_source_name'];
          }
          if (val1 === null) {
            return 1;
          } else if (val2 === null) {
            return -1;
          }
          val1 = val1.trim();
          val2 = val2.trim();
          return sd === 'desc'
            ? val2.localeCompare(val1, undefined, { caseFirst: 'upper' })
            : val1.localeCompare(val2, undefined, { caseFirst: 'upper' });
        case 'int':
          // numbers can come back as string
          val1 = parseInt(a[sb]) || 0;
          val2 = parseInt(b[sb]) || 0;
          break;
        case 'date':
          // date case
          val1 = new Date(a[sb]);
          val2 = new Date(b[sb]);
          break;
        default:
        // If new type or not defined sort by default
      }

      return val1 > val2 ? gt : lt;
    });

    this.setState({
      data: dataSortedBy
    });
  }

  showSearch = () => this.name === 'user' || this.name === 'content';

  showFilters = () => this.name === 'content';

  getSearchLabel = () => {
    let label = translatr('web.common.main', 'SearchBy');
    let tab = this.name;

    if (tab === 'content') {
      label += ' ' + translatr('web.common.main', 'CardTitle');
    } else if (tab === 'user') {
      label += ' ' + translatr('web.common.main', 'UserName');
    } else if (tab === 'group') {
      label += ' ' + translatr('web.common.main', 'GroupName');
    } else if (tab === 'channel') {
      label += ' ' + translatr('web.common.main', 'ChannelName');
    }
    return label;
  };

  getSearchStr = e => {
    this.setState({ searchStr: e.target.value });
  };

  onDateChange(newDate) {
    if (newDate.length !== 2) return;

    const fromDate = this.formatDate(newDate[0]);
    const toDate = this.formatDate(newDate[1]);

    window.sessionStorage.setItem('analytics-from-date', fromDate);
    window.sessionStorage.setItem('analytics-to-date', toDate);

    this.setState(
      {
        fromDate,
        toDate,
        page: 1
      },
      this.getData
    );
  }

  onFilterChange = () => {
    //
  };

  async onPageChange(page) {
    this.setState(
      {
        page
      },
      this.getData
    );
  }

  // This is the click event from the TableData item
  handleClick = async (
    e,
    event,
    rollupTable = 'user_performance_hourly_rollups_i',
    label = 'Data',
    payload
  ) => {
    // Admin -> Settings -> Team Config
    if (!this.allowSubadminDrilldown && this.state.groupSpecificPerf !== true) {
      return;
    }
    const link = linkTo(event, payload?.type);
    if (link !== '') {
      let [path, attr] = link.split(':');
      if (!path || !attr) {
        console.error('Invalid link');
        return;
      }

      try {
        let attrId = e.target.closest('tr').getAttribute(attr);
        window.open(`${path}${attrId}`, '_blank', 'noopener,noreferrer');
      } catch (err) {
        console.error(err);
      }
      return;
    }

    let opts = {
      table: payload?.table ? payload.table : 'user_card_performance_reporting_i',
      rollupTable,
      fromDate: this.state.fromDate,
      toDate: this.state.toDate,
      event,
      limit: 1000,
      assignor_id: payload?.assignor_id,
      groupSpecificPerf: this.state.groupSpecificPerf,
      teams:
        this.state.teams &&
        (this.state.teamSelected === 'all'
          ? this.state.teams.map(t => t.id)
          : [this.state.teamSelected])
    };

    // if (map.filter === 'user_id') {
    try {
      let attrEl = e.target.closest('tr');

      if (attrEl.hasAttribute('data-user-id')) {
        opts.userId = parseInt(attrEl.getAttribute('data-user-id'));
      }

      if (attrEl.hasAttribute('data-card-id')) {
        opts.cardId = parseInt(attrEl.getAttribute('data-card-id'));
      }

      if (attrEl.hasAttribute('data-card-author-id')) {
        opts.cardAuthorId = parseInt(attrEl.getAttribute('data-card-author-id'));
      }

      if (attrEl.hasAttribute('data-team-id')) {
        opts.teamId = parseInt(attrEl.getAttribute('data-team-id'));
      }

      if (attrEl.hasAttribute('data-team-ids')) {
        opts.teamIds = attrEl
          .getAttribute('data-team-ids')
          .split(',')
          .map(id => parseInt(id));
      }

      if (attrEl.hasAttribute('data-channel-id')) {
        opts.channelId = parseInt(attrEl.getAttribute('data-channel-id'));
      }

      if (attrEl.hasAttribute('data-assignment-identifier')) {
        opts.assignment_identifier = attrEl.getAttribute('data-assignment-identifier');
      }
      if (attrEl.hasAttribute('data-channel-identifier')) {
        opts.channel_identifier = attrEl.getAttribute('data-channel-identifier');
      }
      if (attrEl.hasAttribute('data-user-identifier')) {
        opts.user_identifier = attrEl.getAttribute('data-user-identifier');
      }
      if (attrEl.hasAttribute('data-card-identifier')) {
        opts.card_identifier = attrEl.getAttribute('data-card-identifier');
      }
      if (attrEl.hasAttribute('data-group-identifier')) {
        opts.group_identifier = attrEl.getAttribute('data-group-identifier');
      }
    } catch (err) {}
    // }

    if (rollupTable && opts.teams?.length === 1) {
      opts.teamId = opts.teams[0];
      delete opts.teams;
    }

    // Open the modal
    this.setState({
      modalOpen: !this.state.modalOpen,
      modalDataLoaded: false,
      modalTitle: label
    });

    // Make our request for the info
    let hadError = false;
    let resp = await getUserPerformance(opts).catch(err => {
      console.error('Performance handleClick.func', err);
      hadError = true;
    });

    // We need to run this twice if the modal fails to respond
    if (hadError) {
      resp = await getUserPerformance(opts).catch(err => {
        console.error('Performance handleClick.func2', err);
      });
    }

    // If it came back successful
    // TODO: Add messaging saying there's a failure?
    let data = [];
    let headers = [];
    let total = 0;
    if (resp && resp.facts_data) {
      // if (map.type === 'table') {
      headers = Object.keys(resp?.facts_data?.[0] || []).map(key => {
        // return {
        //   sortable: true,
        //   name: tr(key)
        // };
        return key;
      });
      data = resp.facts_data;
      let drilldown = getDrilldown(event);
      if (drilldown) {
        headers = drilldown.filter(item => headers.includes(item));
      }
      data = updateData(headers, data);
      total = resp.total;
      // }

      // Not using lists currently
      // if (map.type === 'list') {
      //   let data = data.map(d => {
      //     return {
      //       name: d.Name,
      //       handle: d.handle
      //     };
      //   });
      //   this.setState({
      //     modalDataLoaded: true,
      //     modalChild: <ListWithSearch data={data} />
      //   });
      // }
    }

    this.setState({
      modalDataLoaded: true,
      // modalChild: <TableList headers={headers} data={data} />
      modalHeaders: headers,
      modalData: data,
      totalModalData: total
    });
    // }
  };

  handleNavClick() {
    console.log('nav clicked!');
  }

  closeFiltersDropdown = e => {
    if (e) {
      if (!this.filtersOpenRef.contains(e.target)) {
        this.setState({ filtersOpen: false }, () => {
          window.removeEventListener('click', this.closeFiltersDropdown);
        });
      }
    } else {
      this.setState({ filtersOpen: false });
    }
  };
  openFiltersDropdown = e => {
    e.stopPropagation();
    this.setState({ filtersOpen: true }, () => {
      window.addEventListener('click', this.closeFiltersDropdown);
    });
  };

  toggleCardTypeFilterDropdown() {
    this.setState({ cardTypeFilterDroppedDown: !this.state.cardTypeFilterDroppedDown });
  }

  downloadCSV = () => {
    let csv = 'data:text/csv;charset=utf-8,';
    csv += this.state.headers.join(',') + '\n';

    let rows = this.state.data.map(row => {
      let dataRow = [];
      this.state.headers.forEach(h => {
        let output = row[h]?.toString?.() || ''; // Our default output;
        if (h === 'author' && output === '') {
          output = row['card_source_name']?.toString?.() || '';
        }
        // If it's specifically a date
        if (output instanceof Date) {
          output = output.toLocaleString();
        }
        // Just a catch otherwise, so we can update as needed
        if (typeof output == 'object') {
          output = output.toString();
        }
        output = output.replace(/#/g, '%23'); // Replace # symbols. You cannot wrap these in quotes either
        output = output.replace(/"/g, '""');

        if (typeof output === 'string') {
          output = decodeHtml(output);
        }
        if (output.search(/("|,|\n)/g) >= 0) {
          output = '"' + output + '"';
        }
        dataRow.push(output);
      });
      return dataRow;
    });
    csv += rows.map(f => f.join(',')).join('\n');

    let encodedUri = encodeURI(csv);
    let link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', `my_data.csv`);
    document.body.appendChild(link);
    link.click();

    setTimeout(() => {
      link.remove();
    }, 1000);
  };
}

export default Performance;
