import React from 'react';
import PropTypes from 'prop-types';
import Flatpickr from 'react-flatpickr';
import Calander from 'centralized-design-system/src/MUIComponents/icons/Calander';
import './DatePicker.scss';
import { tr } from 'edc-web-sdk/helpers/translations';

const DatePicker = props => {
  const fromDate = new Date(props.fromDate).toISOString();
  const toDate = new Date(props.toDate).toISOString();
  return (
    <div className="analytics-date-picker navigation-action">
      {props.label ? (
        <>
          <div className="calendar-icon">
            <Calander width="16" height="16" />
          </div>
          <label className="calendar-label">{tr(props.label)}</label>
        </>
      ) : (
        <div className="calendar-icon">
          <Calander />
        </div>
      )}

      <Flatpickr
        options={{
          inline: false,
          position: 'below',
          mode: 'range',
          onChange: newDate => {
            props.onDateChange(newDate);
          },
          defaultDate: [fromDate, toDate],
          dateFormat: 'm-d-Y'
        }}
      />
      <span className="calendar-dd"></span>
    </div>
  );
};

DatePicker.propTypes = {
  onDateChange: PropTypes.func,
  fromDate: PropTypes.string,
  label: PropTypes.string,
  toDate: PropTypes.string
};

export default DatePicker;
