@import '~flatpickr/dist/themes/light.css';
@import '~centralized-design-system/src/MUIComponents/styles/style.scss';
$color: #595979;

.analytics-date-picker {
  z-index: 3;
  position: relative;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  border-radius: var(--ed-border-radius-sm) !important;
  border: var(--ed-border-size-sm) solid $darkergrey !important;

  &.border-0 {
    border: 0;
  }

  .label {
    display: inline-block;
    font-size: 0.8rem;
    margin-right: var(--ed-spacing-2xs);
    color: $color;
    white-space: nowrap;
  }

  input.flatpickr-input {
    padding: 0;
    color: $color;
    border: none !important;
    width: 13.5rem;
    text-align: center;
    margin-left: calc(-1 * var(--ed-spacing-base));
    margin-right: calc(-1 * var(--ed-spacing-base));
    background: transparent;
  }

  .calendar-label {
    text-align: center;
    margin-left: var(--ed-spacing-2xs);
    margin-bottom: var(--ed-spacing-5xs) !important;
    padding-top: var(--ed-spacing-5xs);
    font-weight: var(--ed-font-weight-semibold);
    font-size: var(--ed-font-size-2xs);
  }

  .calendar-icon {
    margin: 0 0 calc(-1 * var(--ed-spacing-4xs)) 0;
    svg {
      height: rem-calc(16) !important;
      width: rem-calc(16) !important;
      path {
        fill: $darkergrey;
      }
    }
  }

  .calendar-dd {
    width: 1rem;
    z-index: -1;

    &::before {
      content: '';
      display: block;
      position: absolute;
      right: 0.4375rem;
      border: 0.35625rem solid transparent;
      border-top-color: #6f708b;
      top: var(--ed-spacing-2xs);
    }
  }
}

.flatpickr-calendar {
  width: rem-calc(250);
  font-size: var(--ed-font-size-2xs);
}

.dayContainer {
  width: rem-calc(250);
  min-width: rem-calc(250);
  max-width: rem-calc(250);
}

.flatpickr-weekdays {
  width: rem-calc(250);
}

span.flatpickr-weekday {
  color: $darkergrey;
}

.flatpickr-day {
  color: $darkergrey;
  fill: $darkergrey;
}

.flatpickr-day {
  &.selected,
  &.startRange,
  &.endRange,
  &.startRange:hover,
  &.endRange:hover {
    background-color: $color !important;
    border-color: $color !important;
  }
}

.flatpickr-months {
  .flatpickr-month,
  .flatpickr-next-month,
  .flatpickr-prev-month {
    color: $darkergrey;
    fill: $darkergrey;
  }
}

.flatpickr-day {
  &.inRange {
    box-shadow: rem-calc(-5) 0 0 $lightergrey, rem-calc(5) 0 0 $lightergrey;
    background: $lightergrey;
    border-color: $lightergrey;
  }
}

.flatpickr-day {
  &.today.inRange,
  &:focus,
  &:hover {
    background: $lightergrey;
    border-color: $lightergrey;
  }
}

.flatpickr-day {
  &.nextMonthDay {
    &.inRange,
    &.today.inRange,
    &:focus,
    &:hover {
      background: $lightergrey;
      border-color: $lightergrey;
    }
  }
}

.flatpickr-day {
  &.prevMonthDay {
    &.inRange,
    &.today.inRange,
    &:focus,
    &:hover {
      background: $lightergrey;
      border-color: $lightergrey;
    }
  }
}
