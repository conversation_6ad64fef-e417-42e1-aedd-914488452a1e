@import '~centralized-design-system/src/Styles/_variables.scss';

.ed-ui {
  .create-opportunity-alert-modal__content {
    &--description,
    &--filter-list {
      font-size: var(--ed-font-size-sm);
      color: var(--ed-neutral-2);
      line-height: var(--ed-line-height-xs);
    }
    &--description {
      margin-bottom: var(--ed-spacing-base);
    }
    &--filter-list {
      margin: var(--ed-spacing-base) 0 0 var(--ed-spacing-xs);
      color: var(--ed-neutral-3);
      list-style-type: none;
      display: inline-block;
      li {
        display: inline-block;

        span {
          padding: 0 var(--ed-spacing-2xs);
          color: var(--ed-neutral-6);
        }

        &:last-child {
          span {
            display: none;
          }
        }
      }
    }
    &--filter-label {
      font-size: var(--ed-font-size-sm) !important;
      font-weight: var(--ed-font-weight-normal);
      color: var(--ed-neutral-2);
      line-height: var(--ed-line-height-xs);
      display: inline;
    }
  }
}
